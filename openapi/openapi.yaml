openapi: 3.0.1
info:
  title: OpenAPI definition
  version: v0
servers:
  - url: https://rvc.dev.jlr-vcdp.com
    description: Generated server url for dev
  - url: https://rvc.pre-prod.jlr-vcdp.com
    description: Generated server url for pre-prod
  - url: https://rvc.cn-dev.jlr-vcdp.dcclouds.com
    description: Generated server url for cn-dev
  - url: https://rvc.cn-preprod.jlr-vcdp.dcclouds.com
    description: Generated server url for cn-preprod
tags:
  - name: Beep And Flash Command Service
    description: Beep the horn and flash the headlights for a given vehicle
  - name: Cabin Air Clean Command Service
    description: Cabin Air Clean Start Stop Command Service API
  - name: Alarm Reset Command Service
    description: Reset Alarm for a given vehicle
  - name: Target Max SoC Command
    description: Target Max SoC Command Service API
  - name: Remote Door Command Service
    description: Door Lock/Unlock Command Service API
  - name: Charge Cable Lock Command Service
    description: Lock or Unlock the charge cable for a given vehicle
  - name: Charge Door Command Service
    description: Open the charge door (left or right) for a given vehicle
  - name: Climate Control Command Service
    description: Climate Control Command Service API
  - name: EV Charge Command Service
    description: EV Start Stop Command Service API
  - name: Heated Surface Command Service
    description: Heated Surface for a given vehicle
paths:
  /vehicle/{vehicleId}/heatedsurface/steeringwheel:
    post:
      tags:
        - Heated Surface Command Service
      summary: Turn on or off heated steering wheel for a given vehicle.
      operationId: heatedSteeringWheelCommand
      parameters:
        - name: vehicleId
          in: path
          description: Vehicle unique id
          required: true
          schema:
            type: string
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HeatedSteeringWheelCommand'
        required: true
      responses:
        "400":
          description: Invalid Heated Steering Wheel Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example: |2
                  {"errors": {"INVALID_HEATED_STEERING_WHEEL_OPTION": "Invalid heated steering wheel option.
                  Allowed parameters: ON, OFF"}}
        "500":
          description: Error Publishing Command To Hive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  CONNECTION_ERROR: There was an internal communication error.
        "200":
          description: The command was completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HeatedSteeringWheelResponsePayload'
        "404":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_VEHICLE_ID: Vehicle ID not found.
        "409":
          description: Vehicle is in an invalid state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FailedRuleList'
              example: |
                {"failedRules": [{"ENGINE_RUNNING": "Vehicle's engine is running"}]}]}
        "502":
          description: Unsuccessful Response Received From Vehicle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                Command Response Error:
                  description: Command Response Error
                  value: |
                    {"errors": {"UNSUCCESSFUL_COMMAND_RESPONSE": "Command could not be completed.
                    Unsuccessful command response received."}}
                Missing Required Signals:
                  description: Missing Required Signals
                  value:
                    errors:
                      MISSING_REQUIRED_SIGNALS: "Missing required signals: [ElectricParkBreakStatus]"
        "504":
          description: Vehicle did not respond from command
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                No Response Received Error:
                  description: No Response Received Error
                  value:
                    errors:
                      COMMAND_TIMEOUT: Vehicle did not acknowledge the command.
        "202":
          description: Vehicle is already in desired state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleResponse'
              example:
                message: Vehicle is already in desired state.
  /vehicle/{vehicleId}/heatedsurface/seats/{seatId}:
    post:
      tags:
        - Heated Surface Command Service
      summary: Turn on or off heated seat for a given vehicle.
      operationId: heatedSeatsCommand
      parameters:
        - name: vehicleId
          in: path
          description: Vehicle unique id
          required: true
          schema:
            type: string
        - name: seatId
          in: path
          description: ID for seat to effect
          required: true
          schema:
            type: integer
            format: int32
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HeatedSeatRequestPayload'
        required: true
      responses:
        "400":
          description: Invalid Heated Steering Wheel Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example: |2
                  {"errors": {"INVALID_HEATED_STEERING_WHEEL_OPTION": "Invalid heated steering wheel option.
                  Allowed parameters: ON, OFF"}}
        "500":
          description: Error Publishing Command To Hive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  CONNECTION_ERROR: There was an internal communication error.
        "200":
          description: The command was completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HeatedSteeringWheelResponsePayload'
        "404":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_VEHICLE_ID: Vehicle ID not found.
        "409":
          description: Vehicle is in an invalid state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FailedRuleList'
              example: |
                {"failedRules": [{"ENGINE_RUNNING": "Vehicle's engine is running"}]}]}
        "502":
          description: Unsuccessful Response Received From Vehicle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                Command Response Error:
                  description: Command Response Error
                  value: |
                    {"errors": {"UNSUCCESSFUL_COMMAND_RESPONSE": "Command could not be completed.
                    Unsuccessful command response received."}}
                Missing Required Signals:
                  description: Missing Required Signals
                  value:
                    errors:
                      MISSING_REQUIRED_SIGNALS: "Missing required signals: [ElectricParkBreakStatus]"
        "504":
          description: Vehicle did not respond from command
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                No Response Received Error:
                  description: No Response Received Error
                  value:
                    errors:
                      COMMAND_TIMEOUT: Vehicle did not acknowledge the command.
        "202":
          description: Vehicle is already in desired state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleResponse'
              example:
                message: Vehicle is already in desired state.
  /vehicle/{vehicleId}/evcharge:
    post:
      tags:
        - EV Charge Command Service
      summary: Start or Stop charging of given vehicle
      operationId: chargeCommand
      parameters:
        - name: chargeOption
          in: query
          description: start or stop charging the vehicle
          schema:
            type: string
        - name: vehicleId
          in: path
          description: Vehicle unique id
          required: true
          schema:
            type: string
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChargeCommand'
        required: true
      responses:
        "400":
          description: Invalid Charge Option
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_CHARGE_OPTION: "Invalid charge option. Allowed parameters:\
                    \ START, STOP"
        "500":
          description: Error Publishing Command To Hive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  CONNECTION_ERROR: There was an internal communication error.
        "404":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_VEHICLE_ID: Vehicle ID not found.
        "200":
          description: The command was completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargeResponsePayload'
        "504":
          description: Vehicle did not respond from command
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                No Response Received Error:
                  description: No Response Received Error
                  value:
                    errors:
                      COMMAND_TIMEOUT: Vehicle did not acknowledge the command.
        "202":
          description: Vehicle is already in desired state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleResponse'
              example:
                message: Vehicle is already in desired state.
        "502":
          description: Unsuccessful Response Received From Vehicle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                Command Response Error:
                  description: Command Response Error
                  value:
                    errors:
                      UNSUCCESSFUL_COMMAND_RESPONSE: Command could not be completed.
                        Unsuccessful command response received.
                Missing Required Signals:
                  description: Missing Required Signals
                  value:
                    errors:
                      MISSING_REQUIRED_SIGNALS: "Missing required signals: [ElectricParkBreakStatus]"
        "409":
          description: Vehicle is in an invalid state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FailedRuleList'
              example:
                failedRules:
                  - ENGINE_RUNNING: Vehicle's engine is running
  /vehicle/{vehicleId}/evcharge/config/target:
    post:
      tags:
        - Target Max SoC Command
      summary: Update the Target Max State of Charge of a given vehicle
      operationId: maxSocCommand
      parameters:
        - name: vehicleId
          in: path
          description: Vehicle unique id
          required: true
          schema:
            type: string
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TargetMaxSoCRequestPayload'
        required: true
      responses:
        "500":
          description: Error Publishing Command To Hive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  CONNECTION_ERROR: There was an internal communication error.
        "404":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_VEHICLE_ID: Vehicle ID not found.
        "400":
          description: Invalid Target Max SoC Option
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_MAX_SOC_OPTION: "Invalid Max SoC option. Allowed parameters:\
                    \ uint32 eg. 1, 48, 72, 100"
        "200":
          description: The command was completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TargetMaxSoCResponsePayload'
        "504":
          description: Vehicle did not respond from command
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                No Response Received Error:
                  description: No Response Received Error
                  value:
                    errors:
                      COMMAND_TIMEOUT: Vehicle did not acknowledge the command.
        "202":
          description: Vehicle is already in desired state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleResponse'
              example:
                message: Vehicle is already in desired state.
        "502":
          description: Unsuccessful Response Received From Vehicle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                Command Response Error:
                  description: Command Response Error
                  value:
                    errors:
                      UNSUCCESSFUL_COMMAND_RESPONSE: Command could not be completed.
                        Unsuccessful command response received.
                Missing Required Signals:
                  description: Missing Required Signals
                  value:
                    errors:
                      MISSING_REQUIRED_SIGNALS: "Missing required signals: [ElectricParkBreakStatus]"
        "409":
          description: Vehicle is in an invalid state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FailedRuleList'
              example:
                failedRules:
                  - ENGINE_RUNNING: Vehicle's engine is running
  /vehicle/{vehicleId}/door:
    post:
      tags:
        - Remote Door Command Service
      summary: 'Lock or unlock given vehicle '
      operationId: doorCommand
      parameters:
        - name: vehicleId
          in: path
          description: Vehicle unique id
          required: true
          schema:
            type: string
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LockUnlockCommand'
        required: true
      responses:
        "500":
          description: Error Publishing Command To Hive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  CONNECTION_ERROR: There was an internal communication error.
        "404":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_VEHICLE_ID: Vehicle ID not found.
        "502":
          description: Unsuccessful Response Received From Vehicle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                Command Response Error:
                  description: Command Response Error
                  value: |
                    {"errors": {"UNSUCCESSFUL_COMMAND_RESPONSE": "Command could not be completed.
                    Unsuccessful command response received."}}
                Missing Required Signals:
                  description: Missing Required Signals
                  value:
                    errors:
                      MISSING_REQUIRED_SIGNALS: "Missing required signals: [ElectricParkBreakStatus]"
        "400":
          description: Invalid Door Option
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_LOCK_OPTION: "Invalid lock option. Allowed parameters: LOCK,\
                    \ UNLOCK"
        "504":
          description: Vehicle did not respond from command
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                No Response Received Error:
                  description: No Response Received Error
                  value:
                    errors:
                      COMMAND_TIMEOUT: Vehicle did not acknowledge the command.
        "200":
          description: The command was completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DoorResponsePayload'
        "202":
          description: Vehicle is already in desired state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleResponse'
              example:
                message: Vehicle is already in desired state.
        "409":
          description: Vehicle is in an invalid state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FailedRuleList'
              example:
                failedRules:
                  - ENGINE_RUNNING: Vehicle's engine is running
  /vehicle/{vehicleId}/climate:
    post:
      tags:
        - Climate Control Command Service
      summary: 'Change climate of given vehicle given vehicle '
      operationId: climateCommand
      parameters:
        - name: climateOption
          in: query
          description: set the temperature for the vehicle
          schema:
            type: string
        - name: vehicleId
          in: path
          description: Vehicle unique id
          required: true
          schema:
            type: string
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClimateCommand'
        required: true
      responses:
        "500":
          description: Error Publishing Command To Hive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  CONNECTION_ERROR: There was an internal communication error.
        "404":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_VEHICLE_ID: Vehicle ID not found.
        "502":
          description: Unsuccessful Response Received From Vehicle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                Command Response Error:
                  description: Command Response Error
                  value: |
                    {"errors": {"UNSUCCESSFUL_COMMAND_RESPONSE": "Command could not be completed.
                    Unsuccessful command response received."}}
                Missing Required Signals:
                  description: Missing Required Signals
                  value:
                    errors:
                      MISSING_REQUIRED_SIGNALS: "Missing required signals: [ElectricParkBreakStatus]"
        "200":
          description: The command was completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClimateResponsePayload'
        "400":
          description: Invalid Climate Option
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_CLIMATE_OPTION: Invalid climate option. Allowed parameter
                    must be an int value
        "504":
          description: Vehicle did not respond from command
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                No Response Received Error:
                  description: No Response Received Error
                  value:
                    errors:
                      COMMAND_TIMEOUT: Vehicle did not acknowledge the command.
        "202":
          description: Vehicle is already in desired state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleResponse'
              example:
                message: Vehicle is already in desired state.
        "409":
          description: Vehicle is in an invalid state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FailedRuleList'
              example:
                failedRules:
                  - ENGINE_RUNNING: Vehicle's engine is running
  /vehicle/{vehicleId}/chargedoor/right:
    post:
      tags:
        - Charge Door Command Service
      summary: Open the right charge door for a given vehicle.
      operationId: chargeDoorCommandRight
      parameters:
        - name: vehicleId
          in: path
          description: Vehicle unique id
          required: true
          schema:
            type: string
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChargeDoorRightCommand'
        required: true
      responses:
        "200":
          description: The command was completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargeDoorLeftResponsePayload'
        "500":
          description: Error Publishing Command To Hive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  CONNECTION_ERROR: There was an internal communication error.
        "502":
          description: Unsuccessful Response Received From Vehicle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                Command Response Error:
                  description: Command Response Error
                  value:
                    errors:
                      UNSUCCESSFUL_COMMAND_RESPONSE: Command could not be completed.
                        Unsuccessful command response received.
                Missing Required Signals:
                  description: Missing Required Signals
                  value:
                    errors:
                      MISSING_REQUIRED_SIGNALS: "Missing required signals: [ElectricParkBreakStatus]"
        "404":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_VEHICLE_ID: Vehicle ID not found.
        "504":
          description: Vehicle did not respond from command
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                No Response Received Error:
                  description: No Response Received Error
                  value:
                    errors:
                      COMMAND_TIMEOUT: Vehicle did not acknowledge the command.
        "400":
          description: Authorization token missing
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example: |
                {"Required request header 'Authorization' is not present"}}
        "409":
          description: Vehicle is in an invalid state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FailedRuleList'
              example:
                failedRules:
                  - VEHICLE_NOT_PARKED: Vehicle is not parked
  /vehicle/{vehicleId}/chargedoor/left:
    post:
      tags:
        - Charge Door Command Service
      summary: Open the left charge door for a given vehicle.
      operationId: chargeDoorCommandLeft
      parameters:
        - name: vehicleId
          in: path
          description: Vehicle unique id
          required: true
          schema:
            type: string
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChargeDoorLeftCommand'
        required: true
      responses:
        "200":
          description: The command was completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargeDoorLeftResponsePayload'
        "500":
          description: Error Publishing Command To Hive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  CONNECTION_ERROR: There was an internal communication error.
        "502":
          description: Unsuccessful Response Received From Vehicle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                Command Response Error:
                  description: Command Response Error
                  value:
                    errors:
                      UNSUCCESSFUL_COMMAND_RESPONSE: Command could not be completed.
                        Unsuccessful command response received.
                Missing Required Signals:
                  description: Missing Required Signals
                  value:
                    errors:
                      MISSING_REQUIRED_SIGNALS: "Missing required signals: [ElectricParkBreakStatus]"
        "404":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_VEHICLE_ID: Vehicle ID not found.
        "504":
          description: Vehicle did not respond from command
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                No Response Received Error:
                  description: No Response Received Error
                  value:
                    errors:
                      COMMAND_TIMEOUT: Vehicle did not acknowledge the command.
        "400":
          description: Authorization token missing
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example: |
                {"Required request header 'Authorization' is not present"}}
        "409":
          description: Vehicle is in an invalid state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FailedRuleList'
              example:
                failedRules:
                  - VEHICLE_NOT_PARKED: Vehicle is not parked
  /vehicle/{vehicleId}/chargecablelock:
    post:
      tags:
        - Charge Cable Lock Command Service
      summary: Lock or Unlock the charge cable for a given vehicle.
      operationId: chargeCableLockCommand
      parameters:
        - name: vehicleId
          in: path
          description: Vehicle unique id
          required: true
          schema:
            type: string
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChargeCableLockRequestPayload'
        required: true
      responses:
        "500":
          description: Error Publishing Command To Hive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  CONNECTION_ERROR: There was an internal communication error.
        "404":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_VEHICLE_ID: Vehicle ID not found.
        "409":
          description: Vehicle is in an invalid state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FailedRuleList'
              example:
                failedRules:
                  - CHARGE_CABLE_NOT_CONNECTED: Charging cable is not connected
        "504":
          description: Vehicle did not respond from command
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                No Response Received Error:
                  description: No Response Received Error
                  value:
                    errors:
                      COMMAND_TIMEOUT: Vehicle did not acknowledge the command.
        "202":
          description: Vehicle is already in desired state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleResponse'
              example:
                message: Vehicle is already in desired state.
        "200":
          description: The command was completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargeCableLockResponsePayload'
        "400":
          description: Invalid Charge Cable Lock Option
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_CHARGE_CABLE_LOCK_OPTION: "Invalid charge cable lock option.\
                    \ Allowed parameter must be LOCK,UNLOCK"
        "502":
          description: Unsuccessful Response Received From Vehicle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                Command Response Error:
                  description: Command Response Error
                  value: |
                    {"errors": {"UNSUCCESSFUL_COMMAND_RESPONSE": "Command could not be completed.
                    Unsuccessful command response received."}}
                Missing Required Signals:
                  description: Missing Required Signals
                  value:
                    errors:
                      MISSING_REQUIRED_SIGNALS: "Missing required signals: [WiredConnectStatus]"
  /vehicle/{vehicleId}/cabinairclean:
    post:
      tags:
        - Cabin Air Clean Command Service
      summary: Send cabin air clean command to given vehicle
      operationId: cabinAirCleanCommand
      parameters:
        - name: cabinAirCleanOption
          in: query
          description: set the value for cabin air clean command for the vehicle
          schema:
            type: string
        - name: vehicleId
          in: path
          description: Vehicle unique id
          required: true
          schema:
            type: string
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CabinAirCleanCommand'
        required: true
      responses:
        "500":
          description: Error Publishing Command To Hive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  CONNECTION_ERROR: There was an internal communication error.
        "404":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_VEHICLE_ID: Vehicle ID not found.
        "200":
          description: The command was completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CabinAirCleanResponsePayload'
        "400":
          description: Invalid Cabin Air Clean Option
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_CABIN_AIR_CLEAN_OPTION: "Invalid cabin air clean option.\
                    \  Allowed parameters: START, STOP"
        "504":
          description: Vehicle did not respond from command
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                No Response Received Error:
                  description: No Response Received Error
                  value:
                    errors:
                      COMMAND_TIMEOUT: Vehicle did not acknowledge the command.
        "202":
          description: Vehicle is already in desired state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleResponse'
              example:
                message: Vehicle is already in desired state.
        "502":
          description: Unsuccessful Response Received From Vehicle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                Command Response Error:
                  description: Command Response Error
                  value:
                    errors:
                      UNSUCCESSFUL_COMMAND_RESPONSE: Command could not be completed.
                        Unsuccessful command response received.
                Missing Required Signals:
                  description: Missing Required Signals
                  value:
                    errors:
                      MISSING_REQUIRED_SIGNALS: "Missing required signals: [ElectricParkBreakStatus]"
        "409":
          description: Vehicle is in an invalid state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FailedRuleList'
              example:
                failedRules:
                  - ENGINE_RUNNING: Vehicle's engine is running
  /vehicle/{vehicleId}/beepflash:
    post:
      tags:
        - Beep And Flash Command Service
      summary: Beep the horn and flash the headlights for a given vehicle
      operationId: beepFlashCommand
      parameters:
        - name: vehicleId
          in: path
          description: Vehicle unique id
          required: true
          schema:
            type: string
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BeepFlashCommand'
        required: true
      responses:
        "500":
          description: Error Publishing Command To Hive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  CONNECTION_ERROR: There was an internal communication error.
        "404":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_VEHICLE_ID: Vehicle ID not found.
        "409":
          description: Vehicle is in an invalid state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FailedRuleList'
              example:
                failedRules:
                  - VEHICLE_NOT_PARKED: Vehicle is not parked
        "200":
          description: The command was completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BeepFlashResponsePayload'
        "400":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_ALARM_OPTION: "Invalid alarm option. Allowed parameters:\
                    \ OFF"
        "502":
          description: Unsuccessful Response Received From Vehicle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                Command Response Error:
                  description: Command Response Error
                  value: |
                    {"errors": {"UNSUCCESSFUL_COMMAND_RESPONSE": "Command could not be completed.
                    Unsuccessful command response received."}}
                Missing Required Signals:
                  description: Missing Required Signals
                  value:
                    errors:
                      MISSING_REQUIRED_SIGNALS: "Missing required signals: [ElectricParkBreakStatus]"
        "504":
          description: Vehicle did not respond from command
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                No Response Received Error:
                  description: No Response Received Error
                  value:
                    errors:
                      COMMAND_TIMEOUT: Vehicle did not acknowledge the command.
  /vehicle/{vehicleId}/alarm:
    post:
      tags:
        - Alarm Reset Command Service
      summary: Reset Alarm for a given vehicle
      operationId: alarmResetCommand
      parameters:
        - name: vehicleId
          in: path
          description: Vehicle unique id
          required: true
          schema:
            type: string
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AlarmResetCommand'
        required: true
      responses:
        "500":
          description: Error Publishing Command To Hive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  CONNECTION_ERROR: There was an internal communication error.
        "404":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_VEHICLE_ID: Vehicle ID not found.
        "409":
          description: Vehicle is in an invalid state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FailedRuleList'
              example:
                failedRules:
                  - ALARM_NOT_TRIGGERED: Vehicle alarm is not running
        "200":
          description: The command was completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlarmResetResponsePayload'
        "400":
          description: Invalid ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                errors:
                  INVALID_ALARM_OPTION: "Invalid alarm option. Allowed parameters:\
                    \ OFF"
        "502":
          description: Unsuccessful Response Received From Vehicle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                Command Response Error:
                  description: Command Response Error
                  value:
                    errors:
                      UNSUCCESSFUL_COMMAND_RESPONSE: Command could not be completed.
                        Unsuccessful command response received.
                Missing Required Signals:
                  description: Missing Required Signals
                  value:
                    errors:
                      MISSING_REQUIRED_SIGNALS: "Missing required signals: [AlarmModeMS]"
        "504":
          description: Vehicle did not respond from command
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                No Response Received Error:
                  description: No Response Received Error
                  value:
                    errors:
                      COMMAND_TIMEOUT: Vehicle did not acknowledge the command.
components:
  schemas:
    HeatedSteeringWheelCommand:
      type: object
      properties:
        option:
          type: string
          enum:
            - 'ON'
            - 'OFF'
    ErrorMessage:
      type: object
      properties:
        errors:
          type: object
          additionalProperties:
            type: string
    HeatedSteeringWheelResponsePayload:
      type: object
      properties:
        vehicleId:
          type: string
        created:
          type: string
          format: date-time
        option:
          type: string
          enum:
            - 'ON'
            - 'OFF'
    FailedRuleList:
      type: object
      properties:
        failedRules:
          type: array
          items:
            $ref: '#/components/schemas/ResultMessage'
    ResultMessage:
      type: object
      properties:
        ruleKey:
          type: string
        reason:
          type: string
    SimpleResponse:
      type: object
      properties:
        message:
          type: string
    HeatedSeatRequestPayload:
      type: object
      properties:
        temperatureLevel:
          type: integer
          format: int32
        climateArea:
          type: string
          enum:
            - UNSPECIFIED
            - DONT_CARE
            - ALL
            - CUSHION
            - SQUAB
        climateState:
          type: string
          enum:
            - UNSPECIFIED
            - 'ON'
            - 'OFF'
            - INHIBIT
        option:
          type: string
          enum:
            - 'OFF'
            - 'ON'
    ChargeCommand:
      type: object
      properties:
        option:
          type: string
          enum:
            - START
            - STOP
    ChargeResponsePayload:
      type: object
      properties:
        vehicleId:
          type: string
        created:
          type: string
          format: date-time
        option:
          type: string
          enum:
            - START
            - STOP
    TargetMaxSoCRequestPayload:
      type: object
      properties:
        target:
          type: integer
          format: int32
    TargetMaxSoCResponsePayload:
      type: object
      properties:
        vehicleId:
          type: string
        created:
          type: string
          format: date-time
        target:
          type: integer
          format: int32
        option:
          type: string
          enum:
            - MAX
    LockUnlockCommand:
      type: object
      properties:
        option:
          type: string
          enum:
            - LOCK
            - UNLOCK
    DoorResponsePayload:
      type: object
      properties:
        vehicleId:
          type: string
        created:
          type: string
          format: date-time
        option:
          type: string
          enum:
            - LOCK
            - UNLOCK
    ClimateCommand:
      type: object
      properties:
        option:
          type: string
          enum:
            - START
            - STOP
        temperature:
          type: integer
          format: int32
    ClimateResponsePayload:
      type: object
      properties:
        vehicleId:
          type: string
        created:
          type: string
          format: date-time
        temperature:
          type: integer
          format: int32
        option:
          type: string
          enum:
            - START
            - STOP
    ChargeDoorRightCommand:
      type: object
      properties:
        option:
          type: string
          enum:
            - OPEN
    ChargeDoorLeftResponsePayload:
      type: object
      properties:
        vehicleId:
          type: string
        created:
          type: string
          format: date-time
        option:
          type: string
          enum:
            - OPEN
    ChargeDoorLeftCommand:
      type: object
      properties:
        option:
          type: string
          enum:
            - OPEN
    ChargeCableLockRequestPayload:
      type: object
      properties:
        option:
          type: string
          enum:
            - LOCK
            - UNLOCK
    ChargeCableLockResponsePayload:
      type: object
      properties:
        vehicleId:
          type: string
        created:
          type: string
          format: date-time
        option:
          type: string
          enum:
            - LOCK
            - UNLOCK
    CabinAirCleanCommand:
      type: object
      properties:
        option:
          type: string
          enum:
            - START
            - STOP
    CabinAirCleanResponsePayload:
      type: object
      properties:
        vehicleId:
          type: string
        created:
          type: string
          format: date-time
        option:
          type: string
          enum:
            - START
            - STOP
    BeepFlashCommand:
      type: object
      properties:
        option:
          type: string
          enum:
            - BEEP_FLASH
    BeepFlashResponsePayload:
      type: object
      properties:
        vehicleId:
          type: string
        created:
          type: string
          format: date-time
        option:
          type: string
          enum:
            - BEEP_FLASH
    AlarmResetCommand:
      type: object
      properties:
        option:
          type: string
          enum:
            - 'OFF'
    AlarmResetResponsePayload:
      type: object
      properties:
        vehicleId:
          type: string
        created:
          type: string
          format: date-time
        option:
          type: string
          enum:
            - 'OFF'
