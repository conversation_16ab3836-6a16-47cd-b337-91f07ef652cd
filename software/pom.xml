<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
		 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jaguarlandrover.commandrouter</groupId>
    <artifactId>multi-module-lambda</artifactId>
    <packaging>pom</packaging>
    <version>1.0</version>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.shade.plugin.version>3.4.1</maven.shade.plugin.version>
        <maven.compiler.plugin.version>3.11.0</maven.compiler.plugin.version>
        <aws.java.sdk.version>2.20.62</aws.java.sdk.version>
        <aws.lambda.java.version>1.2.0</aws.lambda.java.version>
        <aws.events.java.version>3.11.0</aws.events.java.version>
        <junit5.version>5.9.3</junit5.version>
        <mockito.core.version>5.3.1</mockito.core.version>
        <jackson.version>2.15.0</jackson.version>
        <aws.lambda.java.event.version>3.11.0</aws.lambda.java.event.version>
        <lombok.version>1.18.28</lombok.version>
        <surfire.plugin.version>3.1.0</surfire.plugin.version>
        <jacoco.version>0.8.8</jacoco.version>
        <pitest.maven.version>1.14.1</pitest.maven.version>
        <pitest.junit5.maven.version>1.2.0</pitest.junit5.maven.version>
        <slf4j.version>1.7.36</slf4j.version>
        <log4j.version>2.20.0</log4j.version>
        <aws-lambda-java-log4j2.version>1.5.1</aws-lambda-java-log4j2.version>
        <powertools-cloudformation.version>1.15.0</powertools-cloudformation.version>
        <crac.version>0.1.3</crac.version>
    </properties>

    <modules>
        <module>commandrouterlambda</module>
        <module>enicustomresource</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>${aws.java.sdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>software.amazon.lambda</groupId>
                <artifactId>powertools-cloudformation</artifactId>
                <version>${powertools-cloudformation.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-lambda-java-log4j2</artifactId>
                <version>${aws-lambda-java-log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>4.1.94.Final</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ec2</artifactId>
                <version>${aws.java.sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-lambda-java-core</artifactId>
                <version>${aws.lambda.java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-lambda-java-events</artifactId>
                <version>${aws.lambda.java.event.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-simple</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.crac</groupId>
                <artifactId>org-crac</artifactId>
                <version>${crac.version}</version>
            </dependency>

            <!--    Added until there is a fix for CVE-2023-44487 in mockserver-netty    -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http2</artifactId>
                <version>4.1.100.Final</version>
            </dependency>

            <!-- Test Dependencies -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit5.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.core.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>${mockito.core.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surfire.plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>prepare-unit-test</id>
                        <phase>initialize</phase>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>generate-report</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- Perform mutation tests -->
            <plugin>
                <groupId>org.pitest</groupId>
                <artifactId>pitest-maven</artifactId>
                <version>${pitest.maven.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>org.pitest</groupId>
                        <artifactId>pitest-junit5-plugin</artifactId>
                        <version>${pitest.junit5.maven.version}</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <excludedClasses>
                        <param>com.jaguarlandrover.commandrouter.factory.*</param>
                        <param>com.jaguarlandrover.commandrouter.service.*</param>
                        <param>com.jaguarlandrover.commandrouter.model.*</param>
                        <param>com.jaguarlandrover.enicustomresource.*</param>
                    </excludedClasses>
                    <mutationThreshold>70</mutationThreshold>
                    <targetTests>com.jaguarlandrover.commandrouter*</targetTests>
                    <include>com.jaguarlandrover.commandrouter.CommandRouterHandler</include>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>