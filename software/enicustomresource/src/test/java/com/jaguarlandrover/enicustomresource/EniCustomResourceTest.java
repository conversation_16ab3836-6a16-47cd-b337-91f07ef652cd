package com.jaguarlandrover.enicustomresource;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.amazonaws.services.lambda.runtime.events.CloudFormationCustomResourceEvent;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.services.ec2.Ec2Client;
import software.amazon.awssdk.services.ec2.Ec2ClientBuilder;
import software.amazon.awssdk.services.ec2.model.DescribeNetworkInterfacesRequest;
import software.amazon.awssdk.services.ec2.model.DescribeNetworkInterfacesResponse;
import software.amazon.awssdk.services.ec2.model.NetworkInterface;
import software.amazon.lambda.powertools.cloudformation.Response;

@ExtendWith(MockitoExtension.class)
class EniCustomResourceTest {

  @InjectMocks
  EniCustomResource eniCustomResource = new EniCustomResource();

  @Mock
  Ec2Client mockEc2Client;

  @Mock
  Ec2ClientBuilder mockEc2ClientBuilder;

  MockedStatic<Ec2Client> mockStaticEc2Client;

  @BeforeEach
  void setUp() {
    Mockito.reset();
    mockStaticEc2Client = Mockito.mockStatic(Ec2Client.class);
    mockStaticEc2Client.when(Ec2Client::builder).thenReturn(mockEc2ClientBuilder);
  }

  @AfterEach
  void tearDown() {
    mockStaticEc2Client.close();
  }

  @Test
  void delete_returnSuccess() {
    var response = eniCustomResource.delete(null, null);
    assertEquals(response.getStatus(), Response.Status.SUCCESS);
  }

  @Test
  void create_returnsError_whenVpcEnisIsNotAList() {
    var response = eniCustomResource.create(CloudFormationCustomResourceEvent.builder()
        .withResourceProperties(Map.of("vpce_enis", "not_a_list"))
        .build(), null);
    assertEquals(response.getStatus(), Response.Status.FAILED);
  }

  @Test
  void create_returnsError_whenDescribeNetworkInterfacesCallFails() {
    var eniIds = List.of("eni1", "eni2");

    Mockito.when(mockEc2ClientBuilder.build()).thenReturn(mockEc2Client);
    Mockito.when(mockEc2Client.describeNetworkInterfaces(DescribeNetworkInterfacesRequest.builder()
        .networkInterfaceIds(eniIds)
        .build())).thenThrow(new RuntimeException("an error"));

    var response = eniCustomResource.create(CloudFormationCustomResourceEvent.builder()
        .withResourceProperties(Map.of("vpce_enis", eniIds))
        .build(), null);
    assertEquals(response.getStatus(), Response.Status.FAILED);
  }

  @Test
  void create_returnsSuccess_and_setsIPs_whenDescribeNetworkInterfacesCallIsSuccessful() {
    var eniIds = List.of("eni1", "eni2");

    Mockito.when(mockEc2ClientBuilder.build()).thenReturn(mockEc2Client);
    Mockito.when(mockEc2Client.describeNetworkInterfaces(DescribeNetworkInterfacesRequest.builder()
        .networkInterfaceIds(eniIds)
        .build())).thenReturn(DescribeNetworkInterfacesResponse.builder()
        .networkInterfaces(List.of(
            NetworkInterface.builder()
                .privateIpAddress("*******")
                .build(),
            NetworkInterface.builder()
                .privateIpAddress("*******")
                .build()))
        .build());

    var response = eniCustomResource.create(CloudFormationCustomResourceEvent.builder()
        .withResourceProperties(Map.of("vpce_enis", eniIds))
        .build(), null);
    assertEquals(response.getStatus(), Response.Status.SUCCESS);
    assertEquals(response.toString(),
        "[Status = SUCCESS,NoEcho = false,PhysicalResourceId = ENIPrivateIPResource,JSON = {\"IP0\":\"*******\",\"IP1\":\"*******\",\"IPS\":\"[*******, *******]\"}]");
  }
}