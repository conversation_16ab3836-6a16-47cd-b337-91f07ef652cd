package com.jaguarlandrover.enicustomresource;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.CloudFormationCustomResourceEvent;
import java.util.HashMap;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import software.amazon.awssdk.services.ec2.Ec2Client;
import software.amazon.awssdk.services.ec2.model.DescribeNetworkInterfacesRequest;
import software.amazon.awssdk.services.ec2.model.NetworkInterface;
import software.amazon.lambda.powertools.cloudformation.AbstractCustomResourceHandler;
import software.amazon.lambda.powertools.cloudformation.Response;

public class EniCustomResource extends AbstractCustomResourceHandler {

  private static final Logger LOGGER = LogManager.getLogger(EniCustomResource.class);

  private static final String PHYSICAL_ID = "ENIPrivateIPResource";

  @Override
  protected Response create(CloudFormationCustomResourceEvent cloudFormationCustomResourceEvent,
                            Context context) {
    try {
      LOGGER.info(
          "[com.jaguarlandrover.commandrouter.EniCustomResource] Executing creation / update of custom resource");

      if (!(cloudFormationCustomResourceEvent.getResourceProperties()
          .get("vpce_enis") instanceof List)) {
        throw new RuntimeException("vpce_enis property is not a list");
      }

      var eniIds = (List<String>)
          cloudFormationCustomResourceEvent.getResourceProperties().get("vpce_enis");

      LOGGER.info("[com.jaguarlandrover.commandrouter.EniCustomResource] {}",
          String.format("ENI IDs: %s", eniIds));

      var ec2Client = Ec2Client.builder().build();
      var enis = ec2Client.describeNetworkInterfaces(DescribeNetworkInterfacesRequest.builder()
          .networkInterfaceIds(eniIds).build());
      var ips = enis.networkInterfaces().stream().map(NetworkInterface::privateIpAddress).toList();
      var returnData = new HashMap<String, String>();
      var idx = 0;
      for (var ip : ips) {
        returnData.put(String.format("IP%d", idx), ip);
        idx = idx + 1;
      }
      returnData.put("IPS", ips.toString());
      LOGGER.info("[com.jaguarlandrover.commandrouter.EniCustomResource] {}",
          String.format("ENI IPs: %s", ips));

      return Response.builder()
          .physicalResourceId(PHYSICAL_ID)
          .status(Response.Status.SUCCESS)
          .value(returnData)
          .build();
    } catch (Exception e) {
      LOGGER.error(
          "[com.jaguarlandrover.commandrouter.EniCustomResource] Failed fetching network interfaces",
          e);
      return Response.failed(PHYSICAL_ID);
    }
  }

  @Override
  protected Response update(CloudFormationCustomResourceEvent cloudFormationCustomResourceEvent,
                            Context context) {
    return create(cloudFormationCustomResourceEvent, context);
  }

  @Override
  protected Response delete(CloudFormationCustomResourceEvent cloudFormationCustomResourceEvent,
                            Context context) {
    return Response.builder()
        .status(Response.Status.SUCCESS)
        .build();
  }
}
