/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.commandrouter.mapper;

import static com.jaguarlandrover.commandrouter.mapper.CommandRouterMapper.CHARGE_START_COMMAND;
import static com.jaguarlandrover.commandrouter.mapper.CommandRouterMapper.CHARGE_STOP_COMMAND;
import static com.jaguarlandrover.commandrouter.mapper.CommandRouterMapper.CLIMATE_START_COMMAND;
import static com.jaguarlandrover.commandrouter.mapper.CommandRouterMapper.CLIMATE_STOP_COMMAND;
import static com.jaguarlandrover.commandrouter.mapper.CommandRouterMapper.DOOR_LOCK_COMMAND;
import static com.jaguarlandrover.commandrouter.mapper.CommandRouterMapper.DOOR_UNLOCK_COMMAND;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class CommandRouterMapperTest {

    private static final String VEHICLE_ID = "command-control-123";

    @Test
    @DisplayName("Get command from the path. Path contains door - should return door")
    void getCommandType_door() {

        final String path = "/vehicle/" + VEHICLE_ID + "/door";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPath(path);


        final String commandType = CommandRouterMapper.getCommandType(path);

        assertEquals(CommandRouterMapper.COMMAND_TYPE_DOOR, commandType);
    }

    @Test
    @DisplayName("Get command from the path. Path contains evcharge - should return charge")
    void getCommandType_evCharge() {

        final String path = "/vehicle/" + VEHICLE_ID + "/evcharge";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPath(path);

        final String commandType = CommandRouterMapper.getCommandType(path);

        assertEquals(CommandRouterMapper.COMMAND_TYPE_CHARGE, commandType);
    }

    @Test
    @DisplayName("Get command from the path. Path contains climate - should return climate")
    void getCommandType_climate() {

        final String path = "/vehicle/" + VEHICLE_ID + "/climate";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPath(path);

        final String commandType = CommandRouterMapper.getCommandType(path);

        assertEquals(CommandRouterMapper.COMMAND_TYPE_CLIMATE, commandType);
    }

    @Test
    @DisplayName("Get command from the path. Invalid door lock option - should throw IllegalArgumentException")
    void getCommandOption_invalidLockOption() {

        final String path = "/vehicle/" + VEHICLE_ID + "/door";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayload("le"));
        request.setPath(path);

        assertThrows(IllegalArgumentException.class, () -> CommandRouterMapper.getCommand(request, path));
    }

    @Test
    @DisplayName("Get command from the path. Invalid EV Charge option - should throw IllegalArgumentException")
    void getCommandOption_invalidEvChargeOption() {

        final String path = "/vehicle/" + VEHICLE_ID + "/evcharge";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayload("le"));
        request.setPath(path);

        assertThrows(IllegalArgumentException.class, () -> CommandRouterMapper.getCommand(request, path));
    }

    @Test
    @DisplayName("Get command from the path. Invalid Climate option - should throw IllegalArgumentException")
    void getCommandOption_invalidClimateOption() {

        final String path = "/vehicle/" + VEHICLE_ID + "/climate";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayload("le"));
        request.setPath(path);

        assertThrows(IllegalArgumentException.class, () -> CommandRouterMapper.getCommand(request, path));
    }

    @Test
    @DisplayName("Get command option from the request body. Missing option - should throw IllegalArgumentException")
    void getCommandOption_missingOption() {

        final String path = "/vehicle/" + VEHICLE_ID + "/door";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setPath(path);

        assertThrows(IllegalArgumentException.class, () -> CommandRouterMapper.getCommand(request, path));
    }

    @Test()
    @DisplayName("Get command from the path. Invalid path - should throw IllegalArgumentException")
    void getCommandType_invalidPath() {

        final String path = "/vehicle/" + VEHICLE_ID + "/abc";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPath(path);

        assertThrows(IllegalArgumentException.class, () -> CommandRouterMapper.getCommandType(path));
    }

    @Test()
    @DisplayName("Get door command from the path. Body contains option LOCK - should return door_lock")
    void getLockCommandOption_door() {

        final String path = "/vehicle/" + VEHICLE_ID + "/door";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayload("LOCK"));
        request.setPath(path);

        String result = CommandRouterMapper.getCommand(request, path);

        assertEquals(DOOR_LOCK_COMMAND, result);
    }

    @Test()
    @DisplayName("Get evcharge command from the path. Body contains option START - should return charge_start")
    void getStartCommandOption_evCharge() {

        final String path = "/vehicle/" + VEHICLE_ID + "/evcharge";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayload("START"));
        request.setPath(path);

        String result = CommandRouterMapper.getCommand(request, path);

        assertEquals(CHARGE_START_COMMAND, result);
    }

    @Test()
    @DisplayName("Get climate command from the path. Body contains option START - should return climate_start")
    void getStartCommandOption_climate() {

        final String path = "/vehicle/" + VEHICLE_ID + "/climate";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayload("START"));
        request.setPath(path);

        String result = CommandRouterMapper.getCommand(request, path);

        assertEquals(CLIMATE_START_COMMAND, result);
    }

    @Test()
    @DisplayName("Get door command from the path. Body contains option UNLOCK - should return door_unlock")
    void getUnlockCommandOption_door() {

        final String path = "/vehicle/" + VEHICLE_ID + "/door";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayload("UNLOCK"));
        request.setPath(path);

        String result = CommandRouterMapper.getCommand(request, path);

        assertEquals(DOOR_UNLOCK_COMMAND, result);
    }

    @Test()
    @DisplayName("Get evcharge command from the path. Body contains option STOP- should return charge_stop")
    void getStopCommandOption_evCharge() {

        final String path = "/vehicle/" + VEHICLE_ID + "/evcharge";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayload("STOP"));
        request.setPath(path);

        String result = CommandRouterMapper.getCommand(request, path);

        assertEquals(CHARGE_STOP_COMMAND, result);
    }

    @Test()
    @DisplayName("Get climate command from the path. Body contains option STOP - should return climate_stop")
    void getStopCommandOption_climate() {

        final String path = "/vehicle/" + VEHICLE_ID + "/climate";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayload("STOP"));
        request.setPath(path);

        String result = CommandRouterMapper.getCommand(request, path);

        assertEquals(CLIMATE_STOP_COMMAND, result);
    }

    @Test()
    @DisplayName("Climate command should have valid temperature")
    void testValidTemperatureOption() {

        final String path = "/vehicle/" + VEHICLE_ID + "/climate";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayloadForClimate("START", 18));
        request.setPath(path);

        Integer result = CommandRouterMapper.getTemperature(request, path);
        assertEquals(18, result);
    }

    @Test()
    @DisplayName("Climate command should throw exception if temperature is not valid")
    void testInvalidTemperatureOption() {

        final String path = "/vehicle/" + VEHICLE_ID + "/climate";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayloadForClimate("START", 10));
        request.setPath(path);

        assertThrows(IllegalArgumentException.class, () -> CommandRouterMapper.getTemperature(request, path));
    }

    @Test()
    @DisplayName("Temperature is required when command is climate_start")
    void testRequiredTemperatureOption() {

        final String path = "/vehicle/" + VEHICLE_ID + "/climate";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayload("START"));
        request.setPath(path);


        assertThrows(JSONException.class, () -> CommandRouterMapper.getTemperature(request, path));
    }

    @Test()
    @DisplayName("Temperature is not required when command is climate_stop")
    void testNotRequiredTemperatureOption() {

        final String path = "/vehicle/" + VEHICLE_ID + "/climate";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setPathParameters(Map.of("vehicleId", VEHICLE_ID));
        request.setBody(createOptionPayload("STOP"));
        request.setPath(path);

        Integer result = CommandRouterMapper.getTemperature(request, path);
        assertNull(result);
    }

    String createOptionPayload(String Option) {
        JSONObject body = new JSONObject();
        try {
            body.put("option", Option.toUpperCase());
        } catch (JSONException ex){
            throw new IllegalArgumentException(ex);
        }
        return body.toString();
    }

    String createOptionPayloadForClimate(String option , int temperature) {
        JSONObject body = new JSONObject();
        try {
            body.put("option", option.toUpperCase());
            body.put("temperature", temperature);
        } catch (JSONException ex){
            throw new IllegalArgumentException(ex);
        }
        return body.toString();
    }
}
