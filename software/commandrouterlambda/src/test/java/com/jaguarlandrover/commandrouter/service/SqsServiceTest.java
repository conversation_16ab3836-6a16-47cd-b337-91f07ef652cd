/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.commandrouter.service;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jaguarlandrover.commandrouter.mapper.CommandRouterMapper;
import com.jaguarlandrover.commandrouter.model.TsdpRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class SqsServiceTest {

    private static final String VEHICLE_ID = "command-control-123";
    private static final String PATH_PARAM_KEY_VEHICLE_ID = "vehicleId";
    private static final String PATH_PARAM_KEY_DOOR_OPTION = "doorOption";
    private static final String PATH_PARAM_VALUE_DOOR_OPTION_LOCK = "LOCK";

    private static final String AUTH_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJraWQiOiJ4V2tjNlpsSEE2Zm1iNlJVcHFqT284b2VLUG89IiwiYWxnIjoiUFMyNTYifQ.";

    private static final String AUTHORIZATION = "Authorization";

    private static final String SQS_URL = "https://sqs.eu-west-2.amazonaws.com/123456789012/command-router";
    private final ObjectMapper objectMapper = mock(ObjectMapper.class);
    private final SqsClient sqsClient = mock(SqsClient.class);

    private static final String LOCK_BODY =
            """
            {
                "option": "LOCK"
            }
            """;

    @Test
    @DisplayName("Send successful TSDP request")
    void sendTsdpRequest_successful() {

        final SqsService sqsService = mock(SqsService.class);
        final Map<String, String> headers = Map.of("X-ROUTE", "TSDP", AUTHORIZATION, AUTH_TOKEN);
        final String path = "/vehicle/" + VEHICLE_ID + "/door";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setHttpMethod("POST");
        request.setPathParameters(Map.of(PATH_PARAM_KEY_VEHICLE_ID, VEHICLE_ID, PATH_PARAM_KEY_DOOR_OPTION, PATH_PARAM_VALUE_DOOR_OPTION_LOCK));
        request.setPath(path);
        request.setHeaders(headers);
        request.withBody(LOCK_BODY);

        TsdpRequest tsdpRequest = CommandRouterMapper.buildTsdpRequest(request);

        sqsService.sendTsdpRequest(tsdpRequest, request.getHeaders().get(AUTHORIZATION));

        verify(sqsService, times(1))
                .sendTsdpRequest(any(TsdpRequest.class), anyString());
    }

    @Test
    @DisplayName("Send TSDP request. Object mapper throws JsonProcessingException exception which should be mapped to IllegalArgumentException")
    void sendTsdpRequest_objectMapperThrowsException() throws JsonProcessingException {

        final SqsService sqsService = new SqsService(sqsClient, SQS_URL, objectMapper);
        final Map<String, String> headers = Map.of("X-ROUTE", "TSDP", AUTHORIZATION, AUTH_TOKEN);
        final String path = "/vehicle/" + VEHICLE_ID + "/door";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setHeaders(Map.of(AUTHORIZATION, AUTH_TOKEN));
        request.setHttpMethod("POST");
        request.setPathParameters(Map.of(PATH_PARAM_KEY_VEHICLE_ID, VEHICLE_ID, PATH_PARAM_KEY_DOOR_OPTION, PATH_PARAM_VALUE_DOOR_OPTION_LOCK));
        request.setPath(path);
        request.setHeaders(headers);
        request.withBody(LOCK_BODY);

        TsdpRequest tsdpRequest = CommandRouterMapper.buildTsdpRequest(request);
        when(objectMapper.writeValueAsString(any(Object.class))).thenThrow(new JsonProcessingException("") {});

        assertThrows(IllegalArgumentException.class, () -> sqsService.sendTsdpRequest(tsdpRequest, request.getHeaders().get("Authorization")));
    }

    @Test
    @DisplayName("When beforeCheckpoint() method is called a fake SQS message is sent.")
    void beforeCheckpoint() throws Exception {

        // given
        final SqsService sqsService = new SqsService(sqsClient, SQS_URL, new ObjectMapper());

        // when
        sqsService.beforeCheckpoint(null);


        // then
        final String tsdpRequestString = new ObjectMapper()
          .writeValueAsString(new TsdpRequest("fake-id", "FAKETYPE", "FAKECOMMAND",  0));

        final SendMessageRequest sendMessageRequest = SendMessageRequest.builder()
          .queueUrl(SQS_URL)
                .messageAttributes(Map.of(AUTHORIZATION,
                        MessageAttributeValue.builder()
                                .dataType("String").stringValue("FAKETOKEN").build()))
          .messageBody(tsdpRequestString)
          .build();

        verify(sqsClient, times(1)).sendMessage(sendMessageRequest);
    }

}
