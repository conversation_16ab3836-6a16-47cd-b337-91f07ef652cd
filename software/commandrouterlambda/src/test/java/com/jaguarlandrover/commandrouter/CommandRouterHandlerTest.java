package com.jaguarlandrover.commandrouter;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.jaguarlandrover.commandrouter.model.CommandControlResponse;
import com.jaguarlandrover.commandrouter.model.TsdpRequest;
import com.jaguarlandrover.commandrouter.service.CommandAndControlService;
import com.jaguarlandrover.commandrouter.service.SqsService;
import com.jaguarlandrover.commandrouter.util.HttpConstants;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.Map;
import java.util.stream.Stream;

import static com.jaguarlandrover.commandrouter.util.HttpConstants.X_ROUTE_HEADER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.params.provider.Arguments.of;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CommandRouterHandlerTest {

    private static final String VEHICLE_ID = "command-control-123";
    public static final String STATIC_VEHICLE_ID = "916ef1de-995e-49a4-8539-6c8d1fd1c785";
    private static final String PATH_PARAM_KEY_VEHICLE_ID = "vehicleId";
    private static final String AUTH_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJraWQiOiJ4V2tjNlpsSEE2Zm1iNlJVcHFqT284b2VLUG89IiwiYWxnIjoiUFMyNTYifQ.";

    private final SqsService sqsService = mock(SqsService.class);
    private final CommandAndControlService controlService = mock(CommandAndControlService.class);

    private final CommandRouterHandler commandRouterHandler = new CommandRouterHandler(sqsService, controlService);

    @Test
    @DisplayName("X-ROUTE header present in the request - should return HTTP Code 202 and send message to SQS")
    void handleRequest_xRouteHeaderPresent() throws IOException, InterruptedException {

        final Map<String, String> headers = Map.of(X_ROUTE_HEADER, "TSDP", HttpConstants.AUTH_HEADER, AUTH_TOKEN);
        final String path = "/vehicle/" + VEHICLE_ID + "/door";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setHttpMethod("POST");
        request.setPathParameters(Map.of(PATH_PARAM_KEY_VEHICLE_ID, VEHICLE_ID));
        request.setPath(path);
        request.setHeaders(headers);
        request.setBody(createOptionPayload("LOCK"));

        APIGatewayProxyResponseEvent result = commandRouterHandler.handleRequest(request, null);
        assertEquals(202, result.getStatusCode());
        verify(controlService, times(0)).send(any(), any(), any());
        verify(sqsService, times(1))
                .sendTsdpRequest(any(TsdpRequest.class), anyString());
    }

    @ParameterizedTest(name = "X-ROUTE header not present in the request - should return the same http status {0} returned by command-and-control service and not send message to SQS.")
    @MethodSource("commandControlResponses")
    void handleRequest_noXRouteHeaderNotPresent(int httpStatus, String responseBody) throws IOException, InterruptedException {

        final String path = "/vehicle/" + VEHICLE_ID + "/door";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setHttpMethod("POST");
        request.setPathParameters(Map.of(PATH_PARAM_KEY_VEHICLE_ID, VEHICLE_ID));
        request.setPath(path);
        request.setHeaders(Map.of(HttpConstants.AUTH_HEADER, AUTH_TOKEN));
        request.setBody(createOptionPayload("LOCK"));

        Mockito.when(controlService.send(any(), any(), any())).thenReturn(new CommandControlResponse(httpStatus, responseBody));

        APIGatewayProxyResponseEvent result = commandRouterHandler.handleRequest(request, null);
        assertEquals(httpStatus, result.getStatusCode());
        assertEquals(responseBody, result.getBody());

        verify(sqsService, times(0))
                .sendTsdpRequest(any(TsdpRequest.class), anyString());
    }

    @Test
    @DisplayName("X-ROUTE header not present in the request but vehicleId matches specific TSDP vehicle - should return HTTP Code 202 and send message to SQS")
    void handleRequest_noXRouteHeaderNotPresentButVehicleMatchesSpecific() throws IOException, InterruptedException {
        final String path = "/vehicle/" + STATIC_VEHICLE_ID + "/door";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setHttpMethod("POST");
        request.setBody(createOptionPayload("UNLOCK"));
        request.setPathParameters(Map.of(PATH_PARAM_KEY_VEHICLE_ID, STATIC_VEHICLE_ID));
        request.setPath(path);
        request.setHeaders(Map.of(HttpConstants.AUTH_HEADER, AUTH_TOKEN));

        APIGatewayProxyResponseEvent result = commandRouterHandler.handleRequest(request, null);
        assertEquals(202, result.getStatusCode());
        verify(controlService, times(0)).send(any(), any(), any());
        verify(sqsService, times(1))
            .sendTsdpRequest(any(TsdpRequest.class), anyString());
    }

    @Test
    @DisplayName("Exception thrown by HTTP Client - should return HTTP Code 400")
    void handleRequest_ExceptionThrown1() throws IOException, InterruptedException {

        final Map<String, String> headers = Map.of(X_ROUTE_HEADER, "Command-Control", HttpConstants.AUTH_HEADER, AUTH_TOKEN);
        final String path = "/vehicle/" + VEHICLE_ID + "/door";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setHttpMethod("POST");
        request.setPathParameters(Map.of(PATH_PARAM_KEY_VEHICLE_ID, VEHICLE_ID));
        request.setPath(path);
        request.setHeaders(headers);
        request.setBody(createOptionPayload("LOCK"));

        when(controlService.send(any(), any(), any())).thenThrow(new IllegalArgumentException());

        APIGatewayProxyResponseEvent result = commandRouterHandler.handleRequest(request, null);
        assertEquals(400, result.getStatusCode());
    }

    @Test
    @DisplayName("Exception thrown by HTTP Client - should return HTTP Code 500")
    void handleRequest_ExceptionThrown() throws IOException, InterruptedException {

        final Map<String, String> headers = Map.of(X_ROUTE_HEADER, "Command-Control", HttpConstants.AUTH_HEADER, AUTH_TOKEN);
        final String path = "/vehicle/" + VEHICLE_ID + "/door";

        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent();
        request.setHttpMethod("POST");
        request.setPathParameters(Map.of(PATH_PARAM_KEY_VEHICLE_ID, VEHICLE_ID));
        request.setPath(path);
        request.setHeaders(headers);
        request.setBody(createOptionPayload("LOCK"));

        when(controlService.send(any(), any(), any())).thenThrow(new IOException());

        APIGatewayProxyResponseEvent result = commandRouterHandler.handleRequest(request, null);
        assertEquals(500, result.getStatusCode());
    }



    static Stream<Arguments> commandControlResponses() {
        return Stream.of(
                of(200, """
                        {
                          "vehicleId": "string",
                          "lockStatus": "LOCK"
                        }
                        """),
                of(409, """
                        {
                          "failedRules": [
                            {
                              "ENGINE_RUNNING": "Vehicle's engine is running"
                            }
                          ]
                        }
                        """),
                of(200, """
                           {
                           "vehicleId": "string",
                           "lockStatus": "LOCK"
                         }
                        """),
                of(502, """
                        {
                          "errors": {
                            "UNSUCCESSFUL_COMMAND_CONFIRMATION": "Command could not be completed. Unsuccessful command confirmation response received."
                          }
                        }
                        """)
        );
    }

    String createOptionPayload(String option) {
        JSONObject body = new JSONObject();
        try {
            body.put("option", option.toUpperCase());
        } catch (JSONException ex) {
            throw new IllegalArgumentException(ex);
        }
        return body.toString();
    }
}
