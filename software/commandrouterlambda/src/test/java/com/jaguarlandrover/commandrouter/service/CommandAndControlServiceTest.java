package com.jaguarlandrover.commandrouter.service;

import static com.jaguarlandrover.commandrouter.util.HttpConstants.APPLICATION_JSON;
import static com.jaguarlandrover.commandrouter.util.HttpConstants.AUTH_HEADER;
import static com.jaguarlandrover.commandrouter.util.HttpConstants.CONTENT_TYPE_HEADER;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CommandAndControlServiceTest {

  private final HttpResponse httpResponse = mock(HttpResponse.class);

  private final HttpClient httpClient = mock(HttpClient.class);

  private static final String COMMAND_CONTROL_HOST = "https://command-and-control-test-url.dev.jlr-vcdp.com";

  private static final String AUTH_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJraWQiOiJ4V2tjNlpsSEE2Zm1iNlJVcHFqT284b2VLUG89IiwiYWxnIjoiUFMyNTYifQ.";

  @DisplayName("Service sends successful requests when valid values send in.")
  @Test
  void sendSuccess() throws Exception {

    // given
    CommandAndControlService service = new CommandAndControlService(httpClient, COMMAND_CONTROL_HOST);

    String lockPayload =
            """
              {
                 "option": "LOCK"
              }
            """;

    when(httpResponse.statusCode()).thenReturn(200);
    when(httpResponse.body()).thenReturn(lockPayload);


    // when
    when(httpClient.send(any(), any())).thenReturn(httpResponse);
    service.send("/vehicle/12345/door", lockPayload, AUTH_TOKEN);

    // then
    HttpRequest request = HttpRequest.newBuilder()
      .uri(URI.create(COMMAND_CONTROL_HOST + "/vehicle/12345/door"))
      .headers(CONTENT_TYPE_HEADER, APPLICATION_JSON, AUTH_HEADER, AUTH_TOKEN)
      .POST(HttpRequest.BodyPublishers.ofString(lockPayload))
      .build();

    verify(httpClient, times(1)).send(eq(request), eq(HttpResponse.BodyHandlers.ofString()));
  }

  @DisplayName("Service throws correct exception when httpClient throws an exception.")
  @Test
  void sendFailure() throws Exception {
    // given
    CommandAndControlService service = new CommandAndControlService(httpClient, COMMAND_CONTROL_HOST);

    // when
    when(httpClient.send(any(), any())).thenThrow(new IOException("Test exception."));

    // then.
    assertThrows(IOException.class, () -> service.send("/vehicle/12345/door", "{}", AUTH_TOKEN));
  }

  @DisplayName("Service makes a request with known fake values when beforeCheckpoint() is called.")
  @Test
  void beforeCheckpoint() throws Exception {

    // given
    CommandAndControlService service = new CommandAndControlService(httpClient, COMMAND_CONTROL_HOST);
    when(httpClient.send(any(), any())).thenReturn(httpResponse);

    // when
    service.beforeCheckpoint(null);

    // then
    HttpRequest request = HttpRequest.newBuilder()
      .uri(URI.create(COMMAND_CONTROL_HOST + "/fake/command"))
      .headers(CONTENT_TYPE_HEADER, APPLICATION_JSON, AUTH_HEADER, "fake token")
      .POST(HttpRequest.BodyPublishers.noBody())
      .build();

    verify(httpClient, times(1)).send(eq(request), eq(HttpResponse.BodyHandlers.ofString()));

  }
}