/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.commandrouter;

import static software.amazon.awssdk.http.HttpStatusCode.ACCEPTED;
import static software.amazon.awssdk.http.HttpStatusCode.BAD_REQUEST;
import static software.amazon.awssdk.http.HttpStatusCode.INTERNAL_SERVER_ERROR;
import static software.amazon.awssdk.http.HttpStatusCode.UNAUTHORIZED;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.datadoghq.datadog_lambda_java.DDLambda;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jaguarlandrover.commandrouter.factory.DependencyFactory;
import com.jaguarlandrover.commandrouter.mapper.CommandRouterMapper;
import com.jaguarlandrover.commandrouter.model.CommandControlResponse;
import com.jaguarlandrover.commandrouter.model.TsdpRequest;
import com.jaguarlandrover.commandrouter.service.CommandAndControlService;
import com.jaguarlandrover.commandrouter.service.SqsService;
import com.jaguarlandrover.commandrouter.util.HttpConstants;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.crac.Core;
import org.json.JSONObject;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;

/**
 * A handler function for AWS lambda for routing requests to either the command and
 * control TSDP.
 */
@Slf4j
@SuppressWarnings("squid:S1166") // Sonar complains about catch blocks
public class CommandRouterHandler implements RequestHandler<APIGatewayProxyRequestEvent,
        APIGatewayProxyResponseEvent> {

  private static final String ROUTE_HEADER_VALUE = "tsdp";
  private static final String SQS_URL_ENV_VARIABLE = "sqsUrl";
  private static final String COMMAND_CONTROL_HOST_VARIABLE = "commandControlHost";
  public static final Collection<String> TSDP_VEHICLE_IDS = List.of("916ef1de-995e-49a4-8539-6c8d1fd1c785", "020deb1e-0871-417e-97f4-d48738c1d9c9",
          "e13f2ad2-9dec-471e-964b-935521d6a57f", "c620d5fa-270e-4221-b0e9-f7d6f897d715");
  private static final String TSDP_SQS_REGION_VALUE = "tsdpSqsRegion";
  private final SqsService sqsService;
  private final CommandAndControlService commandAndControlService;

  /**
   * No-args constructor which uses the crac library to prime the function for SnapStart.
   */
  public CommandRouterHandler() {
    this.sqsService = new SqsService(
      SqsClient.builder().region(Region.of(System.getenv(TSDP_SQS_REGION_VALUE)))
          .credentialsProvider(DefaultCredentialsProvider.create()).build(),
      System.getenv(SQS_URL_ENV_VARIABLE), new ObjectMapper());
    this.commandAndControlService = new CommandAndControlService(DependencyFactory.httpClient(),
            System.getenv(COMMAND_CONTROL_HOST_VARIABLE));
    // required to make the dummy calls for SnapStart to capture the JIT compiler result on startup.
    Core.getGlobalContext().register(this.sqsService);
    Core.getGlobalContext().register(this.commandAndControlService);
  }

  /**
   * Test constructor.
   *
   * @param sqsService               An instance of the SQS service for forwarding requests to TSDP.
   * @param commandAndControlService An instance of the command and control service.
   */
  CommandRouterHandler(SqsService sqsService, CommandAndControlService commandAndControlService) {
    this.sqsService = sqsService;
    this.commandAndControlService = commandAndControlService;
  }

  @Override
  public APIGatewayProxyResponseEvent handleRequest(final APIGatewayProxyRequestEvent request,
                                                    final Context context) {

    log.info("Received request with body: {} from: {}", request.getBody(), request.getPath());

    final DDLambda tracer = new DDLambda(request, context);
    final String authToken = request.getHeaders().get(HttpConstants.AUTH_HEADER);

    final APIGatewayProxyResponseEvent response = new APIGatewayProxyResponseEvent();

    if (authToken == null || authToken.isBlank()) {
      JSONObject authErrorBody = new JSONObject();
      authErrorBody.put("message", "Unauthorized");
      response.setBody(authErrorBody.toString());
      response.setStatusCode(UNAUTHORIZED);
      return response;
    }

    try {

      if (isTsdpRequest(request)) {
        log.info("Header {} present. Forwarding request to TSDP.", HttpConstants.X_ROUTE_HEADER);

        //TODO - We do not include temperature for climate command - This needs reviewing
        // & add auth token into request for tsdp.
        final TsdpRequest tsdpRequest = CommandRouterMapper.buildTsdpRequest(request);
        sqsService.sendTsdpRequest(tsdpRequest, authToken);

        response.setStatusCode(ACCEPTED);

      } else {
        log.info("Forwarding request to Command And Control.");

        CommandControlResponse serverResponse = commandAndControlService
                .send(request.getPath(), request.getBody(), authToken);

        response.setBody(serverResponse.body());
        response.setStatusCode(serverResponse.statusCode());
      }

    } catch (IllegalArgumentException illegalArgumentException) {
      response.setStatusCode(BAD_REQUEST);
      response.setBody(illegalArgumentException.getMessage());
      log.error("Error: {} Returning http status code: {}",
              illegalArgumentException.getMessage(), response.getStatusCode());
    } catch (IOException e) {
      log.error(e.getMessage());
      response.setStatusCode(INTERNAL_SERVER_ERROR);
      log.error("Error: {} Returning http status code: {}", e.getMessage(),
              response.getStatusCode());
    } catch (InterruptedException e) {
      log.error("Error: {}", e.getMessage());
      response.setStatusCode(INTERNAL_SERVER_ERROR);
      Thread.currentThread().interrupt();
    }

    tracer.finish();
    log.info("Returning response with http status code: {} and body: {}",
            response.getStatusCode(), request.getBody());
    return response;
  }

  private static boolean isTsdpRequest(APIGatewayProxyRequestEvent request) {
    final String xRoute = request.getHeaders().get(HttpConstants.X_ROUTE_HEADER);
    final boolean isSpecifiedVehicle = TSDP_VEHICLE_IDS.stream().anyMatch(vehicle -> request.getPath().contains(vehicle));
    return (xRoute != null && xRoute.equalsIgnoreCase(ROUTE_HEADER_VALUE)) || isSpecifiedVehicle;
  }
}
