/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.commandrouter.model;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import java.util.Locale;

@Slf4j
@Data
public class OptionValidation {

  private DoorOptions doorOptions;
  private EvChargeOptions evChargeOptions;
  private ClimateOptions climateOptions;

  public enum DoorOptions {
    LOCK,
    UNLOCK;


    public static DoorOptions convert(String source) {
      try {
        return DoorOptions.valueOf(source.toUpperCase((Locale.ROOT)));
      } catch (IllegalArgumentException e) {
        log.error("Error when converting door option. Error: {}", e.getMessage());
        throw new IllegalArgumentException("INVALID_LOCK_OPTION, Invalid lock option. "
          + "Allowed parameters: LOCK, UNLOCK");
      }
    }
  }

  public enum EvChargeOptions {
    START,
    STOP;

    public static EvChargeOptions convert(String source) {
      try {
        return EvChargeOptions.valueOf(source.toUpperCase((Locale.ROOT)));
      } catch (IllegalArgumentException e) {
        log.error("Error when converting ev charge option. Error: {}", e.getMessage());
        throw new IllegalArgumentException("INVALID_CHARGE_OPTION, Invalid charge option. "
          + "Allowed parameters: START, STOP");
      }
    }
  }

  public enum ClimateOptions {
    START,
    STOP;

    public static ClimateOptions convert(String source) {
      try {
        return ClimateOptions.valueOf(source.toUpperCase((Locale.ROOT)));
      } catch (IllegalArgumentException e) {
        log.error("Error when converting climate option. Error: {}", e.getMessage());
        throw new IllegalArgumentException("INVALID_CLIMATE_OPTION, Invalid climate option. "
          + "Allowed parameters: START, STOP");
      }
    }
  }
}


