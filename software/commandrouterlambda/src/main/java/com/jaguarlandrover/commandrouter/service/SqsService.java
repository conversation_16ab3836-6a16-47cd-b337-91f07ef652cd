/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.commandrouter.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jaguarlandrover.commandrouter.model.TsdpRequest;
import lombok.extern.slf4j.Slf4j;
import org.crac.Context;
import org.crac.Resource;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;
import java.util.Map;


/**
 * A class for integrating with the TSDP handler service via SQS.
 *
 * <p>
 * This implements the crac Resource interface to perform warm up calls that are used to create
 * a SnapShot of the JVM after the lambda is first called. </p>
 */
@Slf4j
public class SqsService implements Resource {

  public static final String FAKE_ID_VEHICLE_FOR_SNAPSTART = "fake-id";
  public static final String FAKE_COMMAND_FOR_SNAPSTART = "FAKECOMMAND";
  public static final String FAKE_COMMAND_TYPE_FOR_SNAPSTART = "FAKETYPE";
  public static final String FAKE_AUTH_TOKEN_STATE_FOR_SNAPSTART = "FAKETOKEN";
  public static final Integer FAKE_TEMPERATURE_FOR_SNAPSTART = 0;
  private final String sqsUrl;
  private final ObjectMapper objectMapper;
  private final SqsClient sqsClient;


  /**
   * Constructor.
   *
   * @param sqsClient    An instance of the AWS sqs client.
   * @param sqsUrl       The URL for the SQS.
   * @param objectMapper An instance of the object mapper for serialising objects.
   */
  public SqsService(SqsClient sqsClient, String sqsUrl, ObjectMapper objectMapper) {
    this.sqsClient = sqsClient;
    this.sqsUrl = sqsUrl;
    this.objectMapper = objectMapper;
  }

  /**
   * Send a tsdp request to SQS.
   *
   * @param tsdpRequest The event to send to SQS.
   * @param authToken   The auth token.
   * @throws IllegalArgumentException .Thrown when there is an error mapping the input.
   */
  public void sendTsdpRequest(TsdpRequest tsdpRequest, String authToken)
      throws IllegalArgumentException {

    try {
      final String tsdpRequestString = objectMapper.writeValueAsString(tsdpRequest);

      final SendMessageRequest sendMessageRequest = SendMessageRequest.builder()
        .queueUrl(sqsUrl)
        .messageAttributes(Map.of("Authorization",
          MessageAttributeValue.builder().stringValue(authToken)
            .dataType("String").build()))
        .messageBody(tsdpRequestString)
        .build();

      sqsClient.sendMessage(sendMessageRequest);
      log.info("Sent request to TSDP with body: {}", tsdpRequest);

    } catch (JsonProcessingException e) {
      log.error("Error when sending TSDP request. Error: {}", e.getMessage());
      throw new IllegalArgumentException(e);
    }
  }

  @Override
  public void beforeCheckpoint(Context<? extends Resource> context) {
    try {
      log.info("beforeCheckpoint() called. SQS url is: {}", sqsUrl);
      // doesn't matter that this payload is invalid.
      // We need to call the API so SnapStart can capture the JIT compiler result on start up.
      sendTsdpRequest(
        new TsdpRequest(FAKE_ID_VEHICLE_FOR_SNAPSTART, FAKE_COMMAND_TYPE_FOR_SNAPSTART,
          FAKE_COMMAND_FOR_SNAPSTART,
          FAKE_TEMPERATURE_FOR_SNAPSTART),
        FAKE_AUTH_TOKEN_STATE_FOR_SNAPSTART);
    } catch (Exception ex) {
      // don't need to do anything with the error since it is part of the warmup process.
      log.error("Error: {}", ex);
    }
  }

  @Override
  public void afterRestore(Context<? extends Resource> context) {
    // not used.
  }
}
