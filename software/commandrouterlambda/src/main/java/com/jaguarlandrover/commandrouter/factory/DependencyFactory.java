/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.commandrouter.factory;

import com.jaguarlandrover.commandrouter.CommandRouterHandler;
import java.net.http.HttpClient;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.core.SdkSystemSetting;
import software.amazon.awssdk.http.urlconnection.UrlConnectionHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;

/**
 * The module containing all dependencies required by the {@link CommandRouterHandler}.
 */
public class DependencyFactory {

  private DependencyFactory() {
  }

  /**
   * Create an instance of SqsClient.
   *
   * @return an instance of SqsClient
   */
  public static SqsClient sqsClient(AwsCredentialsProvider provider) {
    return SqsClient.builder()
      .credentialsProvider(provider)
      .region(Region.of(System.getenv(SdkSystemSetting.AWS_REGION.environmentVariable())))
      .httpClientBuilder(UrlConnectionHttpClient.builder())
      .build();
  }

  /**
   * Create an instance of HttpClient.
   *
   * @return an instance of HttpClient
   */
  public static HttpClient httpClient() {
    return HttpClient.newHttpClient();
  }

}
