package com.jaguarlandrover.commandrouter.service;

import static com.jaguarlandrover.commandrouter.util.HttpConstants.APPLICATION_JSON;
import static com.jaguarlandrover.commandrouter.util.HttpConstants.AUTH_HEADER;
import static com.jaguarlandrover.commandrouter.util.HttpConstants.CONTENT_TYPE_HEADER;

import com.jaguarlandrover.commandrouter.model.CommandControlResponse;
import lombok.extern.slf4j.Slf4j;
import org.crac.Context;
import org.crac.Resource;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

/**
 * A service for integrating with the Command and Control API.
 *
 * <p>
 * This implements the crac Resource interface to perform warm up calls that are used to create a SnapShot of the JVM
 * after the lambda is first called.</p>
 *
 */
@Slf4j
public class CommandAndControlService implements Resource {

  public static final String FAKE_REQUEST_PATH_FOR_SNAPSTART = "/fake/command";
  public static final String FAKE_REQUEST_BODY_FOR_SNAPSTART = "{}";
  public static final String FAKE_ACCESS_TOKEN_FOR_SNAPSTART = "fake token";

  private final HttpClient httpClient;
  private final String commandAndControlHostUrl;


  /**
   * Creates an instance of this class.
   *
   * @param httpClient An instance of the HttpClient to make calls to the API.
   *
   * @param commandAndControlHostUrl The host name of the command and control server.
   */
  public CommandAndControlService(HttpClient httpClient, String commandAndControlHostUrl) {
    this.httpClient = httpClient;
    this.commandAndControlHostUrl = commandAndControlHostUrl;
  }

  /**
   * Calls the command and control API.
   *
   * @param path the request path for the endpoint to call (should not include the host.)
   * @param requestBody the request body.
   *
   * @return The response from the server.
   * @throws IOException caused by a client error.
   * @throws InterruptedException other errors (such as timeout).
   */
  public CommandControlResponse send(String path, String requestBody, String authToken)
      throws IOException, InterruptedException {
    log.debug("send({}, {}) called.", path, requestBody);
    final HttpRequest commandRequest = buildCommandControlHttpRequest(path, requestBody, authToken);

    HttpResponse<String> response = httpClient.send(commandRequest, HttpResponse.BodyHandlers.ofString());
    log.info("Sent request to command and control with body: {}", requestBody);
    log.info("Response http code from command and control: {}", response.statusCode());

    return new CommandControlResponse(response.statusCode(), response.body());
  }

  private HttpRequest buildCommandControlHttpRequest(String path, String requestBody, String authToken) {
    log.debug("buildCommandControlHttpRequest({}, {}) called.", path, requestBody);
    HttpRequest.BodyPublisher bodyPublisher;

    if (requestBody != null && !requestBody.isBlank()) {
      bodyPublisher = HttpRequest.BodyPublishers.ofString(requestBody);
    } else {
      bodyPublisher = HttpRequest.BodyPublishers.noBody();
    }

    return HttpRequest.newBuilder()
      .uri(URI.create(commandAndControlHostUrl + path))
      .headers(CONTENT_TYPE_HEADER, APPLICATION_JSON, AUTH_HEADER, authToken)
      .POST(bodyPublisher)
      .build();
  }

  @Override
  public void beforeCheckpoint(Context<? extends Resource> context) throws Exception {
    try {
      log.debug("beforeCheckpoint() called. Command and control host url is: {}", commandAndControlHostUrl);
      // call the API with fake URL just so SnapStart can capture the JIT compiler result on start up. Do not use the response.
      send(FAKE_REQUEST_PATH_FOR_SNAPSTART, FAKE_REQUEST_BODY_FOR_SNAPSTART, FAKE_ACCESS_TOKEN_FOR_SNAPSTART);
    } catch (InterruptedException e) {
      log.error("Error: {}", e.getMessage());
      Thread.currentThread().interrupt();
    } catch (Exception ex) {
      log.error("Error: {}", ex);
    }
  }

  @Override
  public void afterRestore(Context<? extends Resource> context) {
    // not used.
  }
}
