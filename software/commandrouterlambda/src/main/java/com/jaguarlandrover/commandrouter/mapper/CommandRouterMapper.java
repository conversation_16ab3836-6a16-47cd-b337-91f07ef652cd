/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.commandrouter.mapper;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.jaguarlandrover.commandrouter.model.OptionValidation;
import com.jaguarlandrover.commandrouter.model.TsdpRequest;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import java.util.Locale;
import java.util.Objects;

@Slf4j
@NoArgsConstructor(force = true, access = AccessLevel.PRIVATE)
public class CommandRouterMapper {

  public static final String COMMAND_TYPE_DOOR = "door";
  public static final String DOOR_LOCK_COMMAND = "door_lock";
  public static final String DOOR_UNLOCK_COMMAND = "door_unlock";

  public static final String COMMAND_TYPE_EVCHARGE = "evcharge";
  public static final String COMMAND_TYPE_CHARGE = "charge";
  public static final String CHARGE_START_COMMAND = "charge_start";
  public static final String CHARGE_STOP_COMMAND = "charge_stop";

  public static final String COMMAND_TYPE_CLIMATE = "climate";
  public static final String CLIMATE_START_COMMAND = "climate_start";
  public static final String CLIMATE_STOP_COMMAND = "climate_stop";

  private static final String OPTION = "option";
  private static final String PATH_PARAM_VEHICLE_ID = "vehicleId";

  /**
   * Build tsdp request.
   *
   * @param request APIGatewayProxyRequestEvent
   * @return TsdpRequest
   */
  public static TsdpRequest buildTsdpRequest(APIGatewayProxyRequestEvent request) throws IllegalArgumentException {

    String requestPath = request.getPath().toLowerCase(Locale.ROOT);

    try {
      return TsdpRequest.builder()
        .vehicleIdentifier(request.getPathParameters().get(PATH_PARAM_VEHICLE_ID))
        .type(getCommandType(requestPath))
        .command(getCommand(request, requestPath))
        .temperature(getTemperature(request, requestPath))
        .build();
    } catch (Exception e) {
      log.error("Error when building TSDP request. Error: {}", e.getMessage());
      throw new IllegalArgumentException("Invalid request: " + e.getMessage());
    }
  }

  /**
   * Get command type from path. E.g. door , evcharge or climate.
   *
   * @return String
   */
  public static String getCommandType(String requestPath) {

    if (requestPath.contains(COMMAND_TYPE_DOOR)) {
      return COMMAND_TYPE_DOOR;
    } else if (requestPath.contains(COMMAND_TYPE_EVCHARGE)) {
      return COMMAND_TYPE_CHARGE;
    } else if (requestPath.contains(COMMAND_TYPE_CLIMATE)) {
      return COMMAND_TYPE_CLIMATE;
    } else {
      log.error("Could not get command type.");
      throw new IllegalArgumentException("Invalid request path. Could not get command type.");
    }
  }


  /**
   * Get command based on request body. E.g. door_unlock or charge_start.
   *
   * @param request     APIGatewayProxyRequestEvent
   * @return String
   */
  public static String getCommand(APIGatewayProxyRequestEvent request, String requestPath) {

    if (request.getBody() != null) {
      final JSONObject body = new JSONObject(request.getBody());
      String option = body.getString(OPTION);


      if (requestPath.contains(COMMAND_TYPE_EVCHARGE)) {
        if (OptionValidation.EvChargeOptions.convert(option).name().equals("START")) {
          return CHARGE_START_COMMAND;
        } else {
          return CHARGE_STOP_COMMAND;
        }
      } else if (requestPath.contains(COMMAND_TYPE_CLIMATE)) {
        if (OptionValidation.ClimateOptions.convert(option).name().equals("START")) {
          return CLIMATE_START_COMMAND;
        } else {
          return CLIMATE_STOP_COMMAND;
        }
      } else if (requestPath.contains(COMMAND_TYPE_DOOR)) {
        if (OptionValidation.DoorOptions.convert(option).name().equals("LOCK")) {
          return DOOR_LOCK_COMMAND;
        } else {
          return DOOR_UNLOCK_COMMAND;
        }

      }

    } else {
      log.error("Could not get command option from request.");
      throw new IllegalArgumentException("Invalid command option.");
    }

    return null;

  }

  public static Integer getTemperature(APIGatewayProxyRequestEvent request, String requestPath) {
    final JSONObject body = new JSONObject(request.getBody());
    String option = body.getString(OPTION);

    if (!requestPath.contains(COMMAND_TYPE_CLIMATE)
      || (requestPath.contains(COMMAND_TYPE_CLIMATE))
      && Objects.equals(option, OptionValidation.ClimateOptions.STOP.name())) {
      return null;
    }

    int temperature = body.getInt("temperature");
    if (temperature < 16 || temperature > 28) {
      throw new IllegalArgumentException("INVALID_CLIMATE_TEMPERATURE, Temperature must be between 16 and 28");
    }
    return temperature;
  }

}