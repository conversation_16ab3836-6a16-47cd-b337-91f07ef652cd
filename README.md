# Command Router

Command Router repository is a java cdk responsible for creating an AWS gateway
that is used to redirect command and control requests to the command and control service and to tsdp-command-handler service.


## Prerequisites

1) AWS-CLI v2+ Instructions can be found [here](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-prereqs.html).
2) Install CDK V2 using: npm install -g aws-cdk  (requires NodeJs to be installed)


## Package structure

The codebase consists of two separate maven projects. The 'infrastructure' package is for the code what defines the infrastructure
that will be used to provision the resources in the cloud. The 'software' directory is where the code for any lambda functions
is be stored and where the functional code or business logic is sorted. There should be a separate maven project per lambda function.

See the README.md inside each directory to for information on how each build and test the code.


```
project
|
|   .gitlab-ci.yml
|___infrastructure
|   |  pom.xml
|   |  README.md
|   |___src
|       |__main
|       |__test
| 
|___software
    |
    |___commandrouterlambda
        |  pom.xml 
        |  README.md    
        |___src
            |__main
            |__test
    |___enicustomresource
        |  pom.xml 
        |  README.md    
        |___src
            |__main
            |__test

```