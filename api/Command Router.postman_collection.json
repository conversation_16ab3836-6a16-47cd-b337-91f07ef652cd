{"info": {"_postman_id": "654ce2ed-e8f4-49de-8ebd-75e4b4b7b4b2", "name": "Command Router", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29888822"}, "item": [{"name": "RDC", "item": [{"name": "<PERSON>", "item": [{"name": "Lock Door", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"LOCK\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/door", "host": ["{{dev-env}}"], "path": [":vehicleId", "door"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Unlock Door", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"UNLOCK\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/door", "host": ["{{dev-env}}"], "path": [":vehicleId", "door"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}, {"name": "Pre Prod", "item": [{"name": "Lock Door", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"LOCK\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/door", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "door"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Unlock Door", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"UNLOCK\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/door", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "door"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}]}, {"name": "Climate", "item": [{"name": "<PERSON>", "item": [{"name": "Start Climate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"START\",\n    \"temperature\": 11\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/climate", "host": ["{{dev-env}}"], "path": [":vehicleId", "climate"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Stop Climate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"STOP\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/climate", "host": ["{{dev-env}}"], "path": [":vehicleId", "climate"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}], "description": "Requests for using VCDP Developers account in AWS, must be logged in to the VCDP Developers VPN."}, {"name": "Pre Prod", "item": [{"name": "Start Climate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"START\",\n    \"temperature\": 11\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/climate", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "climate"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Stop Climate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"STOP\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/climate", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "climate"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}], "description": "Requests for using VCDP Pre Production account in AWS, must be logged in to the VCDP Pre Production VPN."}], "description": "Requests for the Climate Command functionality"}, {"name": "EV", "item": [{"name": "<PERSON>", "item": [{"name": "Stop Charge", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"STOP\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/evcharge", "host": ["{{dev-env}}"], "path": [":vehicleId", "evcharge"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Start Charge", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"START\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/evcharge", "host": ["{{dev-env}}"], "path": [":vehicleId", "evcharge"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}, {"name": "Pre Prod", "item": [{"name": "Start Charge", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"START\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/evcharge", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "evcharge"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Stop Charge", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"STOP\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/evcharge", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "evcharge"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}]}, {"name": "Cabin Air Clean", "item": [{"name": "<PERSON>", "item": [{"name": "Start Cabin Air Clean", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"START\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/cabinairclean", "host": ["{{dev-env}}"], "path": [":vehicleId", "<PERSON><PERSON><PERSON>an"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Stop Cabin Air Clean", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"STOP\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/cabinairclean", "host": ["{{dev-env}}"], "path": [":vehicleId", "<PERSON><PERSON><PERSON>an"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}, {"name": "Pre Prod", "item": [{"name": "Start Cabin Air Clean", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"START\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/cabinairclean", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "<PERSON><PERSON><PERSON>an"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Stop Cabin Air Clean", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"STOP\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/cabinairclean", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "<PERSON><PERSON><PERSON>an"], "variable": [{"key": "vehicleId", "value": "command-contol-test-vehicle"}]}}, "response": []}]}]}, {"name": "Beep And Flash", "item": [{"name": "<PERSON>", "item": [{"name": "Beep And Flash", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"BEEP_FLASH\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/beepflash", "host": ["{{dev-env}}"], "path": [":vehicleId", "beepflash"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}, {"name": "Pre Prod", "item": [{"name": "Beep And Flash", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"BEEP_FLASH\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/beepflash", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "beepflash"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}]}, {"name": "Alarm Reset", "item": [{"name": "<PERSON>", "item": [{"name": "Alarm Reset", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"OFF\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/alarm", "host": ["{{dev-env}}"], "path": [":vehicleId", "alarm"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}, {"name": "Pre Prod", "item": [{"name": "Alarm Reset", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"OFF\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/alarm", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "alarm"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}]}, {"name": "Charge Door", "item": [{"name": "<PERSON>", "item": [{"name": "Charge Door Left", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"OPEN\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/chargedoor/left", "host": ["{{dev-env}}"], "path": [":vehicleId", "chargedoor", "left"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Charge Door Right", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"OPEN\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/chargedoor/right", "host": ["{{dev-env}}"], "path": [":vehicleId", "chargedoor", "right"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}, {"name": "Pre Prod", "item": [{"name": "Charge Door Left", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"OPEN\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/chargedoor/left", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "chargedoor", "left"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Charge Door Right", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"OPEN\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/chargedoor/right", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "chargedoor", "right"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}]}, {"name": "TargetMaxSoC", "item": [{"name": "<PERSON>", "item": [{"name": "Target Max SoC", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"MAX\",\n    \"target\": 74\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/evcharge/config/target", "host": ["{{dev-env}}"], "path": [":vehicleId", "evcharge", "config", "target"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}, {"name": "Pre Prod", "item": [{"name": "Target Max SoC", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"MAX\",\n    \"target\": 74\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/evcharge/config/target", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "evcharge", "config", "target"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}]}, {"name": "Charge Cable Lock", "item": [{"name": "<PERSON>", "item": [{"name": "Charge Cable Lock", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"LOCK\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/chargecablelock", "host": ["{{dev-env}}"], "path": [":vehicleId", "chargecablelock"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Charge Cable Unlock", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"UNLOCK\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/chargecablelock", "host": ["{{dev-env}}"], "path": [":vehicleId", "chargecablelock"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}, {"name": "Pre Prod", "item": [{"name": "Charge Cable Lock", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"LOCK\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/chargecablelock", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "chargecablelock"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Charge Cable Unlock", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"UNLOCK\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/chargecablelock", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "chargecablelock"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}]}]}, {"name": "Heated Surfaces", "item": [{"name": "<PERSON>", "item": [{"name": "Heated Steering Wheel On", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"ON\",\n    \"temperatureLevel\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/heatedsurface/steeringwheel", "host": ["{{dev-env}}"], "path": [":vehicleId", "heatedsurface", "steeringwheel"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Heated Steering Wheel Off", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"OFF\",\n    \"temperatureLevel\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/heatedsurface/steeringwheel", "host": ["{{dev-env}}"], "path": [":vehicleId", "heatedsurface", "steeringwheel"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Heated Seat On", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"ON\",\n    \"temperatureLevel\": 2,\n    \"climateArea\": \"DONT_CARE\",\n    \"climateState\": \"ON\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/heatedsurface/seats/:seatId", "host": ["{{dev-env}}"], "path": [":vehicleId", "heatedsurface", "seats", ":seatId"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}, {"key": "seatId", "value": "2"}]}}, "response": []}, {"name": "Heated Seat Off", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"OFF\",\n    \"temperatureLevel\": 1,\n    \"climateArea\": 2,\n    \"climateState\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-env}}/:vehicleId/heatedsurface/seats/:seatId", "host": ["{{dev-env}}"], "path": [":vehicleId", "heatedsurface", "seats", ":seatId"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}, {"key": "seatId", "value": "2"}]}}, "response": []}], "description": "Requests for using VCDP Developers account in AWS, must be logged in to the VCDP Developers VPN."}, {"name": "Pre Prod", "item": [{"name": "Heated Steering Wheel On", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"ON\",\n    \"temperatureLevel\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/heatedsurface/steeringwheel", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "heatedsurface", "steeringwheel"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Heated Steering Wheel Off", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"OFF\",\n    \"climateArea\": \"DONT_CARE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/heatedsurface/steeringwheel", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "heatedsurface", "steeringwheel"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}]}}, "response": []}, {"name": "Heated Seat On", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"ON\",\n    \"temperatureLevel\": 2,\n    \"climateArea\": \"DONT_CARE\",\n    \"climateState\": \"ON\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/heatedsurface/seats/:seatId", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "heatedsurface", "seats", ":seatId"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}, {"key": "seatId", "value": "2"}]}}, "response": []}, {"name": "Heated Seat Off", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"option\": \"OFF\",\n    \"climateArea\": \"DONT_CARE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{pre-prod-env}}/:vehicleId/heatedsurface/seats/:seatId", "host": ["{{pre-prod-env}}"], "path": [":vehicleId", "heatedsurface", "seats", ":seatId"], "variable": [{"key": "vehicleId", "value": "command-control-test-vehicle"}, {"key": "seatId", "value": "2"}]}}, "response": []}], "description": "Requests for using VCDP Pre Production account in AWS, must be logged in to the VCDP Pre Production VPN."}]}], "auth": {"type": "oauth2", "oauth2": [{"key": "accessTokenUrl", "value": "https://{{iam_host_dev}}/gateway/oauth2/realms/root/realms/customer/access_token", "type": "string"}, {"key": "authUrl", "value": "https://{{iam_host_dev}}/gateway/oauth2/realms/root/realms/customer/authorize", "type": "string"}, {"key": "refreshRequestParams", "value": [], "type": "any"}, {"key": "tokenRequestParams", "value": [], "type": "any"}, {"key": "authRequestParams", "value": [], "type": "any"}, {"key": "tokenName", "value": "Forgerock", "type": "string"}, {"key": "challengeAlgorithm", "value": "S256", "type": "string"}, {"key": "scope", "value": "openid email profile urn:iam2-mgd-v1:scopes:customer:person urn:iam2-mgd-v1:scopes:vehicle:vehicle-identity", "type": "string"}, {"key": "redirect_uri", "value": "oneapp-rangerover://oauth2redirect", "type": "string"}, {"key": "grant_type", "value": "authorization_code_with_pkce", "type": "string"}, {"key": "clientId", "value": "oneapp-rangerover", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}, {"key": "client_authentication", "value": "header", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "dev-env", "value": "https://rvc.dev.jlr-vcdp.com/vehicle", "type": "string"}, {"key": "pre-prod-env", "value": "https://rvc.pre-prod.jlr-vcdp.com/vehicle", "type": "string"}, {"key": "iam_host_dev", "value": "qa.identity.jaguarlandrover.com", "type": "string"}, {"key": "iam_host_pre_prod", "value": "int.identity.jaguarlandrover.com", "type": "string"}]}