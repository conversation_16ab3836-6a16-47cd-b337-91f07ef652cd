environment: vcdp-developers
commandControlHost: https://command-and-control.dev.jlr-vcdp.com
vpcId: vpc-00685e46ff738b205
subnets:
  - subnet-0e69cf767f7daf4d5
  - subnet-0c4c59d2a5e1c3456
  - subnet-07e39cb689a6ad079
customDomain: rvc.dev.jlr-vcdp.com
privateHostedZoneId: Z01299863CSWENUCTTXFW
privateHostedZoneName: dev.jlr-vcdp.com
certArn: arn:aws:acm:eu-west-2:************:certificate/36f8205a-e925-48b9-b3c7-717ec0e19b67
apiGatewayEndpointService: com.amazonaws.eu-west-2.execute-api
eniCustomResourceUuid: f32585aa-2741-400b-a2d9-7b4767105c85
vpnCidr: *********/22
mobileAppsBackendVpcId: vpc-024a7030a3ef2a58e
sqsUrl: https://sqs.eu-west-1.amazonaws.com/************/tsdp-command-queue
tsdpSqsQueueArn: arn:aws:sqs:eu-west-1:************:tsdp-command-queue
ddLambdaForwarderArn: arn:aws:lambda:eu-west-2:************:function:DatadogIntegration-ForwarderStack-ACYVX8-Forwarder-a0qDu0rkablR
ddLayerArn: arn:aws:lambda:eu-west-2:************:layer:dd-trace-java:12