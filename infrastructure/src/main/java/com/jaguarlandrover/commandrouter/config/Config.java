/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.commandrouter.config;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Config {

  private String environment;
  private String commandControlHost;
  private String vpcId;
  private List<String> subnets;
  private String customDomain;
  private String privateHostedZoneId;
  private String privateHostedZoneName;
  private String certArn;
  private String apiGatewayEndpointService;
  private String eniCustomResourceUuid;
  private String vpnCidr;
  private String mobileAppsBackendVpcId;
  private String sqsUrl;
  private String tsdpSqsQueueArn;
  private String ddLambdaForwarderArn;
  private String ddLayerArn;
}
