/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.commandrouter.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.File;
import java.io.IOException;
import java.util.Objects;

@NoArgsConstructor(force = true, access = AccessLevel.PRIVATE)
public class ConfigLoader {

  private static final String CONFIG_DIR = "config";
  private static final Logger LOGGER = LoggerFactory.getLogger(ConfigLoader.class);

  public static Config loadConfiguration(String environment) {
    final File file;
    try {
      file = new File(Objects.requireNonNull(Thread.currentThread().getContextClassLoader()
        .getResource(String.format("%s/%s.yaml", CONFIG_DIR, environment))).getFile());
    } catch (Exception e) {
      throw new RuntimeException(String.format("Unable to start app, environment config for environment: %s not found", environment), e);
    }

    final ObjectMapper om = new ObjectMapper(new YAMLFactory());

    try {
      return om.readValue(file, Config.class);
    } catch (IOException e) {
      LOGGER.error(e.getMessage(), e);
      throw new RuntimeException("Unable to start app, configuration not loaded", e);
    }
  }
}
