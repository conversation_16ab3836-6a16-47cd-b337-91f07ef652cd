/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.commandrouter;

import static com.jaguarlandrover.commandrouter.config.ConfigLoader.loadConfiguration;

import com.jaguarlandrover.commandrouter.config.Config;
import software.amazon.awscdk.App;
import software.amazon.awscdk.Environment;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.Tags;

public class InfrastructureApp {

  private static final String DD_ENV_TAG = "env";
  private static final String DD_SERVICE_TAG = "service";
  private static final String DD_VERSION_TAG = "version";
  private static final String MANAGED_BY_TAG = "managed_by";
  private static final String PII_TAG = "pii_data_handler";
  private static final String SQUAD_TAG = "squad";

  public static void main(final String[] args) {

    Environment env = Environment.builder().account(System.getenv("CDK_DEFAULT_ACCOUNT"))
      .region(System.getenv("CDK_DEFAULT_REGION")).build();
    StackProps stackProps = StackProps.builder().env(env).build();

    String environment = System.getenv("CI_ENVIRONMENT_NAME");
    Config config = loadConfiguration(environment);

    App app = new App();
    new InfrastructureStack(app, "CommandRouterStack", stackProps, config);

    Tags.of(app).add(DD_ENV_TAG, environment);
    Tags.of(app).add(DD_SERVICE_TAG, "command-router");
    Tags.of(app).add(DD_VERSION_TAG, "1.0");
    Tags.of(app).add(MANAGED_BY_TAG, "cdk");
    Tags.of(app).add(PII_TAG, "false");
    Tags.of(app).add(SQUAD_TAG, "kommando_teapots");

    app.synth();
  }
}
