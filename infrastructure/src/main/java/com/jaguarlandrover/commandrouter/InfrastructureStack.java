/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.commandrouter;

import com.jaguarlandrover.commandrouter.codehash.CodeHash;
import com.jaguarlandrover.commandrouter.config.Config;
import org.json.JSONObject;
import software.amazon.awscdk.*;
import software.amazon.awscdk.services.apigateway.AccessLogField;
import software.amazon.awscdk.services.apigateway.AccessLogFormat;
import software.amazon.awscdk.services.apigateway.DomainNameOptions;
import software.amazon.awscdk.services.apigateway.EndpointConfiguration;
import software.amazon.awscdk.services.apigateway.EndpointType;
import software.amazon.awscdk.services.apigateway.IntegrationResponse;
import software.amazon.awscdk.services.apigateway.LambdaIntegration;
import software.amazon.awscdk.services.apigateway.LogGroupLogDestination;
import software.amazon.awscdk.services.apigateway.MethodLoggingLevel;
import software.amazon.awscdk.services.apigateway.MethodResponse;
import software.amazon.awscdk.services.apigateway.MockIntegration;
import software.amazon.awscdk.services.apigateway.PassthroughBehavior;
import software.amazon.awscdk.services.apigateway.RestApi;
import software.amazon.awscdk.services.apigateway.StageOptions;
import software.amazon.awscdk.services.certificatemanager.Certificate;
import software.amazon.awscdk.services.certificatemanager.ICertificate;
import software.amazon.awscdk.services.ec2.IInterfaceVpcEndpoint;
import software.amazon.awscdk.services.ec2.ISubnet;
import software.amazon.awscdk.services.ec2.IVpc;
import software.amazon.awscdk.services.ec2.InterfaceVpcEndpoint;
import software.amazon.awscdk.services.ec2.InterfaceVpcEndpointProps;
import software.amazon.awscdk.services.ec2.InterfaceVpcEndpointService;
import software.amazon.awscdk.services.ec2.Peer;
import software.amazon.awscdk.services.ec2.Port;
import software.amazon.awscdk.services.ec2.SecurityGroup;
import software.amazon.awscdk.services.ec2.Subnet;
import software.amazon.awscdk.services.ec2.SubnetSelection;
import software.amazon.awscdk.services.ec2.Vpc;
import software.amazon.awscdk.services.ec2.VpcLookupOptions;
import software.amazon.awscdk.services.elasticloadbalancingv2.ApplicationLoadBalancer;
import software.amazon.awscdk.services.elasticloadbalancingv2.ApplicationLoadBalancerProps;
import software.amazon.awscdk.services.elasticloadbalancingv2.ApplicationProtocol;
import software.amazon.awscdk.services.elasticloadbalancingv2.ApplicationProtocolVersion;
import software.amazon.awscdk.services.elasticloadbalancingv2.ApplicationTargetGroup;
import software.amazon.awscdk.services.elasticloadbalancingv2.ApplicationTargetGroupProps;
import software.amazon.awscdk.services.elasticloadbalancingv2.BaseApplicationListenerProps;
import software.amazon.awscdk.services.elasticloadbalancingv2.HealthCheck;
import software.amazon.awscdk.services.elasticloadbalancingv2.IApplicationLoadBalancer;
import software.amazon.awscdk.services.elasticloadbalancingv2.IApplicationLoadBalancerTarget;
import software.amazon.awscdk.services.elasticloadbalancingv2.IListenerCertificate;
import software.amazon.awscdk.services.elasticloadbalancingv2.ListenerAction;
import software.amazon.awscdk.services.elasticloadbalancingv2.ListenerCertificate;
import software.amazon.awscdk.services.elasticloadbalancingv2.TargetType;
import software.amazon.awscdk.services.elasticloadbalancingv2.targets.IpTarget;
import software.amazon.awscdk.services.iam.AnyPrincipal;
import software.amazon.awscdk.services.iam.Effect;
import software.amazon.awscdk.services.iam.PolicyDocument;
import software.amazon.awscdk.services.iam.PolicyStatement;
import software.amazon.awscdk.services.iam.PolicyStatementProps;
import software.amazon.awscdk.services.lambda.Alias;
import software.amazon.awscdk.services.lambda.CfnFunction;
import software.amazon.awscdk.services.lambda.Code;
import software.amazon.awscdk.services.lambda.Function;
import software.amazon.awscdk.services.lambda.FunctionProps;
import software.amazon.awscdk.services.lambda.IFunction;
import software.amazon.awscdk.services.lambda.ILayerVersion;
import software.amazon.awscdk.services.lambda.LambdaInsightsVersion;
import software.amazon.awscdk.services.lambda.LayerVersion;
import software.amazon.awscdk.services.lambda.Runtime;
import software.amazon.awscdk.services.lambda.SingletonFunction;
import software.amazon.awscdk.services.lambda.SingletonFunctionProps;
import software.amazon.awscdk.services.lambda.Tracing;
import software.amazon.awscdk.services.lambda.Version;
import software.amazon.awscdk.services.logs.FilterPattern;
import software.amazon.awscdk.services.logs.LogGroup;
import software.amazon.awscdk.services.logs.RetentionDays;
import software.amazon.awscdk.services.logs.SubscriptionFilterOptions;
import software.amazon.awscdk.services.logs.destinations.LambdaDestination;
import software.amazon.awscdk.services.route53.ARecord;
import software.amazon.awscdk.services.route53.HostedZone;
import software.amazon.awscdk.services.route53.HostedZoneAttributes;
import software.amazon.awscdk.services.route53.IHostedZone;
import software.amazon.awscdk.services.route53.RecordTarget;
import software.amazon.awscdk.services.route53.targets.LoadBalancerTarget;
import software.constructs.Construct;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class InfrastructureStack extends Stack {

  private static final String COMMAND_ROUTER = "command-router";

  public InfrastructureStack(final Construct scope, final String id, final StackProps props,
                             final Config config) {

    super(scope, id, props);

    if (props.getEnv() == null) {
      throw new IllegalArgumentException("Missing aws environment object.");
    }

    if (config == null) {
      throw new IllegalArgumentException("Missing config environment object.");
    }

    Environment awsEnv = props.getEnv();

    IVpc vpc = Vpc.fromLookup(this, "command-router-ivpc",
        VpcLookupOptions.builder().vpcId(config.getVpcId())
            .ownerAccountId(awsEnv.getAccount()).region(awsEnv.getRegion()).build());

    SecurityGroup securityGroup = SecurityGroup.Builder.create(this, "command-router-sg")
        .securityGroupName(COMMAND_ROUTER)
        .vpc(vpc)
        .allowAllOutbound(true)
        .build();

    // INGRESS
    securityGroup.addIngressRule(Peer.ipv4(config.getVpnCidr()), Port.tcp(443),
        "Allow HTTPS connections from VPN");
    securityGroup.addIngressRule(Peer.ipv4(config.getVpnCidr()), Port.tcp(80),
        "Allow HTTP connections from VPN");
    securityGroup.addIngressRule(Peer.ipv4(vpc.getVpcCidrBlock()), Port.tcp(443),
        "Allow HTTPS connections from VPC");
    securityGroup.addIngressRule(Peer.ipv4(vpc.getVpcCidrBlock()), Port.tcp(80),
        "Allow HTTP connections from VPC");

    // EGRESS
    securityGroup.addEgressRule(Peer.ipv4(vpc.getVpcCidrBlock()), Port.tcp(443),
        "Allow HTTPS connections to VPC");
    securityGroup.addEgressRule(Peer.ipv4(vpc.getVpcCidrBlock()), Port.tcp(80),
        "Allow HTTP connections to VPC");

    final List<ISubnet> subnets = config.getSubnets().stream().map(subnet ->
        Subnet.fromSubnetId(this,
            "command-router-" + config.getSubnets().indexOf(subnet), subnet)).toList();

    SubnetSelection subnetSelection = SubnetSelection.builder()
        .subnets(subnets)
        .build();

    Function routerHandlerFunction =
        createRouterHandlerFunction(this, awsEnv, config, vpc,
            securityGroup, subnetSelection
            );

    routerHandlerFunction.addToRolePolicy(new PolicyStatement(PolicyStatementProps.builder()
        .effect(Effect.ALLOW)
        .actions(List.of("sqs:SendMessage"))
        .resources(List.of(config.getTsdpSqsQueueArn()))
        .build()));

    IFunction ddLambdaFunction = Function.fromFunctionArn(this, "dd-forwarder",
        config.getDdLambdaForwarderArn());

    routerHandlerFunction.getLogGroup().addSubscriptionFilter("command-router-lambda-log-group-filter",
        SubscriptionFilterOptions.builder()
            .destination(new LambdaDestination(ddLambdaFunction))
            .filterPattern(FilterPattern.allEvents())
            .build());

    SingletonFunction eniCustomResourceFunction =
        createEniCustomResourceFunction(vpc, securityGroup,
            config.getEniCustomResourceUuid(), subnetSelection);

    InterfaceVpcEndpoint vpcEndpoint =
        createVpcEndpointForApiGateway(this, vpc,
            subnetSelection, config.getApiGatewayEndpointService());

    CustomResource eniCustomResource =
        new CustomResource(this, "ENIPrivateIPResource", CustomResourceProps.builder()
            .serviceToken(eniCustomResourceFunction.getFunctionArn())
            .properties(Map.of("vpce_enis", vpcEndpoint.getVpcEndpointNetworkInterfaceIds()))
            .build());

    IListenerCertificate certificate =
        ListenerCertificate.fromArn(config.getCertArn());

    IApplicationLoadBalancer commandRouterAlb =
        createCommandRouterLoadBalancer(eniCustomResource, vpc, subnetSelection, certificate,
            securityGroup);

    LambdaIntegration lambdaIntegration = createSnapStartIntegration(this, routerHandlerFunction, config);

    createRestApi(lambdaIntegration, config, commandRouterAlb,
        vpcEndpoint);
  }

  private static InterfaceVpcEndpoint createVpcEndpointForApiGateway(
      InfrastructureStack scope, IVpc vpc, SubnetSelection subnetSelection,
      String apiGatewayEndpointService) {
    return new InterfaceVpcEndpoint(scope, "command-router-apigateway-endpoint", InterfaceVpcEndpointProps.builder()
        .vpc(vpc)
        .service(new InterfaceVpcEndpointService(apiGatewayEndpointService))
        .subnets(subnetSelection)
        .build());
  }

  private Function createRouterHandlerFunction(Construct scope, Environment environment,
                                               Config config,
                                               IVpc vpc, SecurityGroup securityGroup,
                                               SubnetSelection subnetSelection) {
    final Map<String, String> lambdaEnvironment = Map.of(
        "account", Objects.requireNonNull(environment.getAccount()),
        "region", Objects.requireNonNull(environment.getRegion()),
        "tsdpSqsRegion", fetchTsdpSqsRegion(config.getTsdpSqsQueueArn()),
        "sqsUrl", config.getSqsUrl(),
        "commandControlHost", config.getCommandControlHost(),
        "JAVA_TOOL_OPTIONS", "-javaagent:\"/opt/java/lib/dd-java-agent.jar\" -XX:+TieredCompilation -XX:TieredStopAtLevel=1",
        "DD_LOGS_INJECTION", "true",
        "DD_JMXFETCH_ENABLED", "false",
        "DD_TRACE_ENABLED", "true"
    );

    FunctionProps functionProps = FunctionProps.builder().environment(lambdaEnvironment)
      .functionName(InfrastructureStack.COMMAND_ROUTER)
      .handler("com.jaguarlandrover.commandrouter.CommandRouterHandler::handleRequest")
      .code(Code.fromAsset("../software/commandrouterlambda/target/CommandRouterLambda.jar"))
      .memorySize(2048)
      .insightsVersion(LambdaInsightsVersion.VERSION_1_0_135_0).timeout(Duration.seconds(30))
      .securityGroups(List.of(securityGroup))
      .vpc(vpc).layers(createLayers(scope, config.getDdLayerArn()))
      .vpcSubnets(subnetSelection)
      .tracing(Tracing.ACTIVE)
      .runtime(Runtime.JAVA_17).build();

    return new Function(this, "command-router-lambda", functionProps);
  }

  private String fetchTsdpSqsRegion(String tsdpSqsQueueArn) {
    String[] parts = tsdpSqsQueueArn.split(":");
    return parts.length >= 4 ? parts[3] : "ERROR_REGION";
  }

  private SingletonFunction createEniCustomResourceFunction(
      IVpc lambdaVpc, SecurityGroup lambdaSg, String eniCustomResourceUuid,
      SubnetSelection subnets) {

    return new SingletonFunction(this, "EniCustomResourceFunction",
        SingletonFunctionProps.builder()
            .handler("com.jaguarlandrover.enicustomresource.EniCustomResource")
            .code(Code.fromAsset(
                "../software/enicustomresource/target/eni-custom-resource-1.0.jar"))
            .runtime(Runtime.JAVA_17)
            .memorySize(512)
            .vpc(lambdaVpc)
            .vpcSubnets(subnets)
            .securityGroups(List.of(lambdaSg))
            .logRetention(RetentionDays.ONE_WEEK)
            .timeout(Duration.seconds(15))
            .uuid(eniCustomResourceUuid)
            .build());
  }

  private IApplicationLoadBalancer createCommandRouterLoadBalancer(CustomResource eniCustomResource,
                                                                   IVpc vpc,
                                                                   SubnetSelection subnetSelection,
                                                                   IListenerCertificate certificate,
                                                                   SecurityGroup securityGroup) {
    List<IApplicationLoadBalancerTarget> targets = List.of(
        new IpTarget(eniCustomResource.getAttString("IP0")),
        new IpTarget(eniCustomResource.getAttString("IP1")));
    ApplicationTargetGroup commandRouterTargetGroup =
        new ApplicationTargetGroup(this, "command-router-target",
            ApplicationTargetGroupProps.builder()
                .port(443)
                .targets(targets)
                .deregistrationDelay(Duration.seconds(10))
                .targetType(TargetType.IP)
                .vpc(vpc)
                .protocol(ApplicationProtocol.HTTPS)
                .protocolVersion(ApplicationProtocolVersion.HTTP1)
                .healthCheck(HealthCheck.builder()
                    .healthyThresholdCount(3)
                    .healthyHttpCodes("200,403")
                    .interval(Duration.seconds(15))
                    .timeout(Duration.seconds(5))
                    .build())
                .build());

    ApplicationLoadBalancer commandRouterNlb =
        new ApplicationLoadBalancer(this, "command-router-nlb",
            ApplicationLoadBalancerProps.builder()
                .vpc(vpc)
                .internetFacing(false)
                .vpcSubnets(subnetSelection)
                .securityGroup(securityGroup)
                .build());

    commandRouterNlb.addListener("command-router-https-listener",
        BaseApplicationListenerProps.builder()
            .certificates(List.of(certificate))
            .port(443)
            .protocol(ApplicationProtocol.HTTPS)
            .defaultAction(ListenerAction.forward(List.of(commandRouterTargetGroup)))
            .build());

    return commandRouterNlb;
  }


  private void createRestApi(final LambdaIntegration lambdaIntegration,
                             final Config config,
                             IApplicationLoadBalancer alb,
                             final IInterfaceVpcEndpoint vpcEndpoint) {

    PolicyStatement statement =
        PolicyStatement.Builder.create()
            .principals(List.of(new AnyPrincipal()))
            .actions(List.of("execute-api:Invoke"))
            .resources(List.of("execute-api:/*"))
            .effect(Effect.ALLOW).build();

    PolicyStatement vpceStatement =
        PolicyStatement.Builder.create()
            .principals(List.of(new AnyPrincipal()))
            .actions(List.of("execute-api:Invoke"))
            .resources(List.of("execute-api:/*"))
            .conditions(
                Map.of("StringNotEquals", Map.of(
                    "aws:SourceVpce", vpcEndpoint.getVpcEndpointId(),
                    "aws:SourceVpc", config.getMobileAppsBackendVpcId())))
            .effect(Effect.DENY).build();

    ICertificate cert = Certificate.fromCertificateArn(this, "command-router-cert",
        config.getCertArn());

    DomainNameOptions dno = DomainNameOptions.builder()
        .domainName(config.getCustomDomain())
        .endpointType(EndpointType.REGIONAL)
        .certificate(cert)
        .build();

    LogGroup logGroup = new LogGroup(this, "command-router-gateway-log-group");

    // Set the log format recommended by DataDog docs: https://docs.datadoghq.com/integrations/amazon_api_gateway/
    AccessLogFormat accessLogFormat = AccessLogFormat.custom((new JSONObject(Map.of(
        "requestId", AccessLogField.contextRequestId(),
        "ip", AccessLogField.contextIdentitySourceIp(),
        "caller", AccessLogField.contextIdentityCaller(),
        "user", AccessLogField.contextIdentityUser(),
        "requestTime", AccessLogField.contextRequestTimeEpoch(),
        "httpMethod", AccessLogField.contextHttpMethod(),
        "resourcePath", AccessLogField.contextResourcePath(),
        "status", AccessLogField.contextStatus(),
        "protocol", AccessLogField.contextProtocol(),
        "responseLength", AccessLogField.contextResponseLength()
    ))).toString());

    StageOptions stageOptions = StageOptions.builder()
        .stageName(getDomainEnv(config))
        .cachingEnabled(false)
        .accessLogDestination(new LogGroupLogDestination(logGroup))
        .accessLogFormat(accessLogFormat)
        .loggingLevel(MethodLoggingLevel.INFO)
        .tracingEnabled(true)
        .metricsEnabled(true)
        .build();

    RestApi api = RestApi.Builder.create(this, "command-router-rest-api")
        .restApiName(COMMAND_ROUTER)
        .domainName(dno)
        .deployOptions(stageOptions)
        .policy(PolicyDocument.Builder.create()
            .statements(List.of(statement, vpceStatement))
            .build())
        .endpointConfiguration(
            EndpointConfiguration.builder()
                .types(List.of(EndpointType.PRIVATE))
                .vpcEndpoints(List.of(vpcEndpoint))
                .build())
        .disableExecuteApiEndpoint(false)
        .build();

    createApiResources(lambdaIntegration, api);

    HostedZoneAttributes hostedZoneAttributes = HostedZoneAttributes.builder()
        .hostedZoneId(config.getPrivateHostedZoneId())
        .zoneName(config.getPrivateHostedZoneName())
        .build();

    IHostedZone hostedZone = HostedZone.fromHostedZoneAttributes(
        this,
        "command-router-hosted-zone",
        hostedZoneAttributes);

    ARecord.Builder.create(this, "command-router-a-record")
        .recordName(dno.getDomainName())
        .zone(hostedZone)
        .target(RecordTarget.fromAlias(new LoadBalancerTarget(alb)))
        .build();
  }

  private static void createApiResources(LambdaIntegration lambdaIntegration,
                                         RestApi api) {
    MockIntegration mockIntegration = MockIntegration.Builder.create()
        .passthroughBehavior(PassthroughBehavior.NEVER)
        .requestTemplates(Map.of("application/json", "{statusCode: 404}"))
        .integrationResponses(List.of(IntegrationResponse.builder()
            .statusCode("404")
            .responseTemplates(Map.of("application/json", "{\"message\": \"Not found.\"}")).build()))
        .build();

    api.getRoot().addResource("{proxy+}").addMethod("ANY", mockIntegration).addMethodResponse(
        MethodResponse.builder()
            .statusCode("404").build()
    );
    api.getRoot().resourceForPath("/vehicle/{vehicleId}/evcharge")
        .addMethod("POST", lambdaIntegration);
    api.getRoot().resourceForPath("/vehicle/{vehicleId}/door")
        .addMethod("POST", lambdaIntegration);
    api.getRoot().resourceForPath("/vehicle/{vehicleId}/climate")
        .addMethod("POST", lambdaIntegration);
    api.getRoot().resourceForPath("/vehicle/{vehicleId}/cabinairclean")
        .addMethod("POST", lambdaIntegration);
    api.getRoot().resourceForPath("/vehicle/{vehicleId}/beepflash")
        .addMethod("POST", lambdaIntegration);
    api.getRoot().resourceForPath("/vehicle/{vehicleId}/evcharge/config/target")
        .addMethod("POST", lambdaIntegration);
    api.getRoot().resourceForPath("/vehicle/{vehicleId}/alarm")
        .addMethod("POST", lambdaIntegration);
    api.getRoot().resourceForPath("/vehicle/{vehicleId}/chargedoor/left")
        .addMethod("POST", lambdaIntegration);
    api.getRoot().resourceForPath("/vehicle/{vehicleId}/chargedoor/right")
        .addMethod("POST", lambdaIntegration);
    api.getRoot().resourceForPath("/vehicle/{vehicleId}/chargecablelock")
        .addMethod("POST", lambdaIntegration);
    api.getRoot().resourceForPath("/vehicle/{vehicleId}/heatedsurface/steeringwheel")
        .addMethod("POST", lambdaIntegration);
    api.getRoot().resourceForPath("/vehicle/{vehicleId}/heatedsurface/seats/{seatId}")
        .addMethod("POST", lambdaIntegration);
  }

  private static List<ILayerVersion> createLayers(Construct scope, String ddLayerArn) {
    return List.of(LayerVersion.fromLayerVersionArn(scope, "DD-LAYER", ddLayerArn));
  }

  private static LambdaIntegration createSnapStartIntegration(Construct scope, Function function, Config config) {
    //snapStart is not support in cn-northwest-1
    if (!getDomainEnv(config).startsWith("cn-")) {
      ((CfnFunction) Objects.requireNonNull(function.getNode().getDefaultChild()))
          .setSnapStart(CfnFunction.SnapStartProperty
              .builder()
              .applyOn("PublishedVersions")
              .build());
    }

    // we need a unique name for each time the code is changed
    String codeSha256 = CodeHash.fileDirectoryJarAndPomSha256ToBase64("../software/");

    Version lambdaVersion = Version.Builder.create(scope, "Version-" + codeSha256.substring(0, 6))
        .lambda(function)
        .build();

    Alias alias = Alias.Builder.create(scope, function.getNode().getId() + "-snapstart-alias")
        .aliasName("snapstart-alias").version(lambdaVersion).build();

    return LambdaIntegration.Builder.create(alias).build();
  }

  private static String getDomainEnv(Config config) {
    return switch (config.getEnvironment()) {
      case "vcdp-developers" -> "dev";
      case "vcdp-pre-production" -> "pre-prod";
      case "vcdp-production" -> "prod";
      case "cn-dev" -> "cn-dev";
      case "cn-preprod" -> "cn-preprod";
      case "cn-prod" -> "cn-prod";
      default -> throw new IllegalArgumentException("Unsupported domain environment.");
    };
  }

}
